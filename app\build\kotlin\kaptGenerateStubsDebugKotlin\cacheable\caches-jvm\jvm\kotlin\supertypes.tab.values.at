/ Header Record For PersistentHashMapValueStorage android.app.Application android.os.Parcelable kotlin.Enum android.os.Parcelable android.os.Parcelable kotlin.Annotation kotlin.Annotation) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback# "androidx.leanback.widget.Presenter' &androidx.fragment.app.FragmentActivity androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback9 8androidx.recyclerview.widget.RecyclerView.ItemDecoration androidx.fragment.app.Fragment androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback# "androidx.leanback.widget.Presenter' &androidx.fragment.app.FragmentActivity androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment!  androidx.viewbinding.ViewBinding androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback!  androidx.viewbinding.ViewBinding androidx.room.RoomDatabase android.os.Parcelable kotlin.Enum) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.appcompat.app.AppCompatActivity!  androidx.viewbinding.ViewBinding androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.fragment.app.Fragment android.os.Parcelable kotlin.Enum androidx.lifecycle.ViewModel androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.lifecycle.ViewModel android.os.Parcelable kotlin.Enum? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment androidx.room.RoomDatabase android.os.Parcelable kotlin.Enum androidx.lifecycle.ViewModel? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.lifecycle.ViewModel androidx.room.RoomDatabase android.os.Parcelable kotlin.Enum androidx.room.RoomDatabase? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState? >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState androidx.fragment.app.Fragment androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback android.os.Parcelable kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.room.RoomDatabase android.os.Parcelable kotlin.Enum android.os.Parcelable androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback android.os.Parcelable kotlin.Enum android.os.Parcelable androidx.fragment.app.Fragment androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.fragment.app.Fragment!  androidx.viewbinding.ViewBinding androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum androidx.fragment.app.Fragment androidx.lifecycle.ViewModel!  androidx.viewbinding.ViewBinding androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel androidx.fragment.app.Fragment androidx.lifecycle.ViewModel!  androidx.viewbinding.ViewBinding androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel android.os.Parcelable kotlin.Enum) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback!  androidx.viewbinding.ViewBinding androidx.lifecycle.ViewModel android.os.Parcelable kotlin.Enum androidx.fragment.app.Fragment androidx.lifecycle.ViewModel!  androidx.viewbinding.ViewBinding androidx.lifecycle.ViewModel kotlin.Enum android.os.Parcelable kotlin.Enum android.os.Parcelable androidx.fragment.app.Fragment androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment androidx.fragment.app.Fragment' &androidx.fragment.app.FragmentActivity androidx.fragment.app.Fragment androidx.lifecycle.ViewModel androidx.fragment.app.Fragment!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding androidx.fragment.app.Fragment!  androidx.viewbinding.ViewBinding androidx.fragment.app.Fragment androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment!  androidx.viewbinding.ViewBinding android.os.Parcelable2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment!  androidx.viewbinding.ViewBinding!  androidx.viewbinding.ViewBinding androidx.fragment.app.Fragment androidx.fragment.app.Fragment android.os.Parcelable2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.fragment.app.Fragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback androidx.fragment.app.Fragment!  androidx.viewbinding.ViewBinding androidx.fragment.app.Fragment androidx.fragment.app.Fragment androidx.fragment.app.Fragment