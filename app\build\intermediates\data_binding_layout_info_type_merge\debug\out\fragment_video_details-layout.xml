<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_video_details" modulePackage="com.tvplayer.webdav" filePath="app\src\main\res\layout\fragment_video_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/fragment_video_details_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="242" endOffset="13"/></Target><Target id="@+id/iv_backdrop" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="12" endOffset="40"/></Target><Target id="@+id/scroll_view" view="ScrollView"><Expressions/><location startLine="21" startOffset="4" endLine="240" endOffset="16"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="39" startOffset="8" endLine="49" endOffset="37"/></Target><Target id="@+id/tv_rating" view="TextView"><Expressions/><location startLine="60" startOffset="12" endLine="72" endOffset="49"/></Target><Target id="@+id/tv_year" view="TextView"><Expressions/><location startLine="75" startOffset="12" endLine="82" endOffset="49"/></Target><Target id="@+id/tv_duration" view="TextView"><Expressions/><location startLine="85" startOffset="12" endLine="92" endOffset="49"/></Target><Target id="@+id/tv_genre" view="TextView"><Expressions/><location startLine="95" startOffset="12" endLine="102" endOffset="49"/></Target><Target id="@+id/tv_file_size" view="TextView"><Expressions/><location startLine="105" startOffset="12" endLine="111" endOffset="57"/></Target><Target id="@+id/tv_overview" view="TextView"><Expressions/><location startLine="116" startOffset="8" endLine="124" endOffset="48"/></Target><Target id="@+id/btn_play" view="Button"><Expressions/><location startLine="127" startOffset="8" endLine="141" endOffset="48"/></Target><Target id="@+id/rv_actors" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="159" startOffset="8" endLine="166" endOffset="39"/></Target><Target id="@+id/tv_filename" view="TextView"><Expressions/><location startLine="176" startOffset="12" endLine="183" endOffset="51"/></Target><Target id="@+id/tv_source_path" view="TextView"><Expressions/><location startLine="186" startOffset="12" endLine="193" endOffset="51"/></Target><Target id="@+id/tv_duration_size" view="TextView"><Expressions/><location startLine="196" startOffset="12" endLine="202" endOffset="57"/></Target><Target id="@+id/btn_back_to_top" view="LinearLayout"><Expressions/><location startLine="207" startOffset="8" endLine="237" endOffset="22"/></Target></Targets></Layout>