<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_video_details" modulePackage="com.tvplayer.webdav" filePath="app\src\main\res\layout\fragment_video_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/fragment_video_details_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="257" endOffset="13"/></Target><Target id="@+id/iv_backdrop" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="12" endOffset="40"/></Target><Target id="@+id/scroll_view" view="ScrollView"><Expressions/><location startLine="21" startOffset="4" endLine="255" endOffset="16"/></Target><Target id="@+id/movie_info_container" view="LinearLayout"><Expressions/><location startLine="45" startOffset="12" endLine="156" endOffset="26"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="52" startOffset="16" endLine="62" endOffset="45"/></Target><Target id="@+id/tv_rating" view="TextView"><Expressions/><location startLine="73" startOffset="20" endLine="85" endOffset="57"/></Target><Target id="@+id/tv_year" view="TextView"><Expressions/><location startLine="88" startOffset="20" endLine="95" endOffset="57"/></Target><Target id="@+id/tv_duration" view="TextView"><Expressions/><location startLine="98" startOffset="20" endLine="105" endOffset="57"/></Target><Target id="@+id/tv_genre" view="TextView"><Expressions/><location startLine="108" startOffset="20" endLine="115" endOffset="57"/></Target><Target id="@+id/tv_file_size" view="TextView"><Expressions/><location startLine="118" startOffset="20" endLine="124" endOffset="65"/></Target><Target id="@+id/tv_overview" view="TextView"><Expressions/><location startLine="129" startOffset="16" endLine="137" endOffset="56"/></Target><Target id="@+id/btn_play" view="Button"><Expressions/><location startLine="140" startOffset="16" endLine="154" endOffset="56"/></Target><Target id="@+id/rv_actors" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="174" startOffset="12" endLine="181" endOffset="43"/></Target><Target id="@+id/tv_filename" view="TextView"><Expressions/><location startLine="191" startOffset="16" endLine="198" endOffset="55"/></Target><Target id="@+id/tv_source_path" view="TextView"><Expressions/><location startLine="201" startOffset="16" endLine="208" endOffset="55"/></Target><Target id="@+id/tv_duration_size" view="TextView"><Expressions/><location startLine="211" startOffset="16" endLine="217" endOffset="61"/></Target><Target id="@+id/btn_back_to_top" view="LinearLayout"><Expressions/><location startLine="222" startOffset="12" endLine="252" endOffset="26"/></Target></Targets></Layout>