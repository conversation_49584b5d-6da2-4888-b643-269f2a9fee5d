<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_video_details" modulePackage="com.tvplayer.webdav" filePath="app\src\main\res\layout\fragment_video_details.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/fragment_video_details_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="256" endOffset="13"/></Target><Target id="@+id/iv_backdrop" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="12" endOffset="40"/></Target><Target id="@+id/movie_info_container" view="LinearLayout"><Expressions/><location startLine="21" startOffset="4" endLine="138" endOffset="18"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="33" startOffset="8" endLine="43" endOffset="37"/></Target><Target id="@+id/tv_rating" view="TextView"><Expressions/><location startLine="54" startOffset="12" endLine="66" endOffset="49"/></Target><Target id="@+id/tv_year" view="TextView"><Expressions/><location startLine="69" startOffset="12" endLine="76" endOffset="49"/></Target><Target id="@+id/tv_duration" view="TextView"><Expressions/><location startLine="79" startOffset="12" endLine="86" endOffset="49"/></Target><Target id="@+id/tv_genre" view="TextView"><Expressions/><location startLine="89" startOffset="12" endLine="96" endOffset="49"/></Target><Target id="@+id/tv_file_size" view="TextView"><Expressions/><location startLine="99" startOffset="12" endLine="105" endOffset="57"/></Target><Target id="@+id/tv_overview" view="TextView"><Expressions/><location startLine="110" startOffset="8" endLine="120" endOffset="37"/></Target><Target id="@+id/btn_play" view="Button"><Expressions/><location startLine="123" startOffset="8" endLine="136" endOffset="49"/></Target><Target id="@+id/scroll_view" view="ScrollView"><Expressions/><location startLine="141" startOffset="4" endLine="254" endOffset="16"/></Target><Target id="@+id/rv_actors" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="173" startOffset="12" endLine="180" endOffset="43"/></Target><Target id="@+id/tv_filename" view="TextView"><Expressions/><location startLine="190" startOffset="16" endLine="197" endOffset="55"/></Target><Target id="@+id/tv_source_path" view="TextView"><Expressions/><location startLine="200" startOffset="16" endLine="207" endOffset="55"/></Target><Target id="@+id/tv_duration_size" view="TextView"><Expressions/><location startLine="210" startOffset="16" endLine="216" endOffset="61"/></Target><Target id="@+id/btn_back_to_top" view="LinearLayout"><Expressions/><location startLine="221" startOffset="12" endLine="251" endOffset="26"/></Target></Targets></Layout>