// Generated by view binder compiler. Do not edit!
package com.tvplayer.webdav.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tvplayer.webdav.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentVideoDetailsBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final LinearLayout btnBackToTop;

  @NonNull
  public final Button btnPlay;

  @NonNull
  public final ImageView ivBackdrop;

  @NonNull
  public final RecyclerView rvActors;

  @NonNull
  public final ScrollView scrollView;

  @NonNull
  public final TextView tvDuration;

  @NonNull
  public final TextView tvDurationSize;

  @NonNull
  public final TextView tvFileSize;

  @NonNull
  public final TextView tvFilename;

  @NonNull
  public final TextView tvGenre;

  @NonNull
  public final TextView tvOverview;

  @NonNull
  public final TextView tvRating;

  @NonNull
  public final TextView tvSourcePath;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView tvYear;

  private FragmentVideoDetailsBinding(@NonNull FrameLayout rootView,
      @NonNull LinearLayout btnBackToTop, @NonNull Button btnPlay, @NonNull ImageView ivBackdrop,
      @NonNull RecyclerView rvActors, @NonNull ScrollView scrollView, @NonNull TextView tvDuration,
      @NonNull TextView tvDurationSize, @NonNull TextView tvFileSize, @NonNull TextView tvFilename,
      @NonNull TextView tvGenre, @NonNull TextView tvOverview, @NonNull TextView tvRating,
      @NonNull TextView tvSourcePath, @NonNull TextView tvTitle, @NonNull TextView tvYear) {
    this.rootView = rootView;
    this.btnBackToTop = btnBackToTop;
    this.btnPlay = btnPlay;
    this.ivBackdrop = ivBackdrop;
    this.rvActors = rvActors;
    this.scrollView = scrollView;
    this.tvDuration = tvDuration;
    this.tvDurationSize = tvDurationSize;
    this.tvFileSize = tvFileSize;
    this.tvFilename = tvFilename;
    this.tvGenre = tvGenre;
    this.tvOverview = tvOverview;
    this.tvRating = tvRating;
    this.tvSourcePath = tvSourcePath;
    this.tvTitle = tvTitle;
    this.tvYear = tvYear;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentVideoDetailsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentVideoDetailsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_video_details, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentVideoDetailsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back_to_top;
      LinearLayout btnBackToTop = ViewBindings.findChildViewById(rootView, id);
      if (btnBackToTop == null) {
        break missingId;
      }

      id = R.id.btn_play;
      Button btnPlay = ViewBindings.findChildViewById(rootView, id);
      if (btnPlay == null) {
        break missingId;
      }

      id = R.id.iv_backdrop;
      ImageView ivBackdrop = ViewBindings.findChildViewById(rootView, id);
      if (ivBackdrop == null) {
        break missingId;
      }

      id = R.id.rv_actors;
      RecyclerView rvActors = ViewBindings.findChildViewById(rootView, id);
      if (rvActors == null) {
        break missingId;
      }

      id = R.id.scroll_view;
      ScrollView scrollView = ViewBindings.findChildViewById(rootView, id);
      if (scrollView == null) {
        break missingId;
      }

      id = R.id.tv_duration;
      TextView tvDuration = ViewBindings.findChildViewById(rootView, id);
      if (tvDuration == null) {
        break missingId;
      }

      id = R.id.tv_duration_size;
      TextView tvDurationSize = ViewBindings.findChildViewById(rootView, id);
      if (tvDurationSize == null) {
        break missingId;
      }

      id = R.id.tv_file_size;
      TextView tvFileSize = ViewBindings.findChildViewById(rootView, id);
      if (tvFileSize == null) {
        break missingId;
      }

      id = R.id.tv_filename;
      TextView tvFilename = ViewBindings.findChildViewById(rootView, id);
      if (tvFilename == null) {
        break missingId;
      }

      id = R.id.tv_genre;
      TextView tvGenre = ViewBindings.findChildViewById(rootView, id);
      if (tvGenre == null) {
        break missingId;
      }

      id = R.id.tv_overview;
      TextView tvOverview = ViewBindings.findChildViewById(rootView, id);
      if (tvOverview == null) {
        break missingId;
      }

      id = R.id.tv_rating;
      TextView tvRating = ViewBindings.findChildViewById(rootView, id);
      if (tvRating == null) {
        break missingId;
      }

      id = R.id.tv_source_path;
      TextView tvSourcePath = ViewBindings.findChildViewById(rootView, id);
      if (tvSourcePath == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tv_year;
      TextView tvYear = ViewBindings.findChildViewById(rootView, id);
      if (tvYear == null) {
        break missingId;
      }

      return new FragmentVideoDetailsBinding((FrameLayout) rootView, btnBackToTop, btnPlay,
          ivBackdrop, rvActors, scrollView, tvDuration, tvDurationSize, tvFileSize, tvFilename,
          tvGenre, tvOverview, tvRating, tvSourcePath, tvTitle, tvYear);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
