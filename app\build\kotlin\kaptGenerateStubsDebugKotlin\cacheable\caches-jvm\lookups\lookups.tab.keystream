  Application android.app  Bundle android.app.Activity  Bundle android.content.Context  Bundle android.content.ContextWrapper  Drawable android.graphics.drawable  Bundle 
android.os  
Parcelable 
android.os  Log android.util  LayoutInflater android.view  View android.view  	ViewGroup android.view  Bundle  android.view.ContextThemeWrapper  findViewById android.view.View  Button android.widget  EditText android.widget  	ImageView android.widget  ProgressBar android.widget  TextView android.widget  Toast android.widget  Bundle #androidx.activity.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  
viewModels androidx.fragment.app  Bundle androidx.fragment.app.Fragment  Button androidx.fragment.app.Fragment  CategoryAdapter androidx.fragment.app.Fragment  EditText androidx.fragment.app.Fragment  HomeFragment androidx.fragment.app.Fragment  
HomeViewModel androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  
MediaCategory androidx.fragment.app.Fragment  MediaPosterAdapter androidx.fragment.app.Fragment  ProgressBar androidx.fragment.app.Fragment  RecyclerView androidx.fragment.app.Fragment  ScannerFragment androidx.fragment.app.Fragment  ScannerViewModel androidx.fragment.app.Fragment  SettingsFragment androidx.fragment.app.Fragment  TextView androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  WebDAVConnectionFragment androidx.fragment.app.Fragment  WebDAVConnectionViewModel androidx.fragment.app.Fragment  WebDAVServer androidx.fragment.app.Fragment  com androidx.fragment.app.Fragment  getValue androidx.fragment.app.Fragment  invoke androidx.fragment.app.Fragment  provideDelegate androidx.fragment.app.Fragment  
viewModels androidx.fragment.app.Fragment  Bundle &androidx.fragment.app.FragmentActivity  
ImageCardView androidx.leanback.widget  	Presenter androidx.leanback.widget  Any "androidx.leanback.widget.Presenter  Boolean "androidx.leanback.widget.Presenter  	Delegates "androidx.leanback.widget.Presenter  Drawable "androidx.leanback.widget.Presenter  
ImageCardView "androidx.leanback.widget.Presenter  Int "androidx.leanback.widget.Presenter  	Presenter "androidx.leanback.widget.Presenter  	ViewGroup "androidx.leanback.widget.Presenter  
ViewHolder "androidx.leanback.widget.Presenter  provideDelegate "androidx.leanback.widget.Presenter  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  	ViewModel androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  Boolean androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  LiveData androidx.lifecycle.ViewModel  
MediaCategory androidx.lifecycle.ViewModel  	MediaItem androidx.lifecycle.ViewModel  MediaScanner androidx.lifecycle.ViewModel  MutableLiveData androidx.lifecycle.ViewModel  Result androidx.lifecycle.ViewModel  SimpleWebDAVClient androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  WebDAVServer androidx.lifecycle.ViewModel  DiffUtil androidx.recyclerview.widget  GridLayoutManager androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  Boolean 2androidx.recyclerview.widget.DiffUtil.ItemCallback  
MediaCategory 2androidx.recyclerview.widget.DiffUtil.ItemCallback  	MediaItem 2androidx.recyclerview.widget.DiffUtil.ItemCallback  Boolean (androidx.recyclerview.widget.ListAdapter  DiffUtil (androidx.recyclerview.widget.ListAdapter  	ImageView (androidx.recyclerview.widget.ListAdapter  Int (androidx.recyclerview.widget.ListAdapter  
MediaCategory (androidx.recyclerview.widget.ListAdapter  	MediaItem (androidx.recyclerview.widget.ListAdapter  ProgressBar (androidx.recyclerview.widget.ListAdapter  R (androidx.recyclerview.widget.ListAdapter  RecyclerView (androidx.recyclerview.widget.ListAdapter  TextView (androidx.recyclerview.widget.ListAdapter  Unit (androidx.recyclerview.widget.ListAdapter  View (androidx.recyclerview.widget.ListAdapter  	ViewGroup (androidx.recyclerview.widget.ListAdapter  
ViewHolder )androidx.recyclerview.widget.RecyclerView  Boolean 1androidx.recyclerview.widget.RecyclerView.Adapter  DiffUtil 1androidx.recyclerview.widget.RecyclerView.Adapter  	ImageView 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  
MediaCategory 1androidx.recyclerview.widget.RecyclerView.Adapter  	MediaItem 1androidx.recyclerview.widget.RecyclerView.Adapter  ProgressBar 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  TextView 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  	ImageView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
MediaCategory 4androidx.recyclerview.widget.RecyclerView.ViewHolder  	MediaItem 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ProgressBar 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  TextView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Unit 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  SerializedName com.google.gson.annotations  R com.tvplayer.webdav  TVPlayerApplication com.tvplayer.webdav  id com.tvplayer.webdav.R  iv_category_icon com.tvplayer.webdav.R.id  iv_new_badge com.tvplayer.webdav.R.id  	iv_poster com.tvplayer.webdav.R.id  progress_bar com.tvplayer.webdav.R.id  tv_category_count com.tvplayer.webdav.R.id  tv_category_name com.tvplayer.webdav.R.id  tv_progress com.tvplayer.webdav.R.id  tv_subtitle com.tvplayer.webdav.R.id  tv_title com.tvplayer.webdav.R.id  Boolean com.tvplayer.webdav.data.model  Float com.tvplayer.webdav.data.model  Int com.tvplayer.webdav.data.model  List com.tvplayer.webdav.data.model  Long com.tvplayer.webdav.data.model  
MediaCategory com.tvplayer.webdav.data.model  	MediaItem com.tvplayer.webdav.data.model  	MediaType com.tvplayer.webdav.data.model  String com.tvplayer.webdav.data.model  
WebDAVFile com.tvplayer.webdav.data.model  WebDAVServer com.tvplayer.webdav.data.model  audioExtensions com.tvplayer.webdav.data.model  	lowercase com.tvplayer.webdav.data.model  setOf com.tvplayer.webdav.data.model  substringAfterLast com.tvplayer.webdav.data.model  subtitleExtensions com.tvplayer.webdav.data.model  videoExtensions com.tvplayer.webdav.data.model  Int ,com.tvplayer.webdav.data.model.MediaCategory  	MediaType ,com.tvplayer.webdav.data.model.MediaCategory  String ,com.tvplayer.webdav.data.model.MediaCategory  Boolean (com.tvplayer.webdav.data.model.MediaItem  Date (com.tvplayer.webdav.data.model.MediaItem  Float (com.tvplayer.webdav.data.model.MediaItem  Int (com.tvplayer.webdav.data.model.MediaItem  List (com.tvplayer.webdav.data.model.MediaItem  Long (com.tvplayer.webdav.data.model.MediaItem  	MediaType (com.tvplayer.webdav.data.model.MediaItem  String (com.tvplayer.webdav.data.model.MediaItem  Boolean )com.tvplayer.webdav.data.model.WebDAVFile  Date )com.tvplayer.webdav.data.model.WebDAVFile  Long )com.tvplayer.webdav.data.model.WebDAVFile  String )com.tvplayer.webdav.data.model.WebDAVFile  audioExtensions )com.tvplayer.webdav.data.model.WebDAVFile  	extension )com.tvplayer.webdav.data.model.WebDAVFile  getAUDIOExtensions )com.tvplayer.webdav.data.model.WebDAVFile  getAudioExtensions )com.tvplayer.webdav.data.model.WebDAVFile  getLOWERCASE )com.tvplayer.webdav.data.model.WebDAVFile  getLowercase )com.tvplayer.webdav.data.model.WebDAVFile  getSUBSTRINGAfterLast )com.tvplayer.webdav.data.model.WebDAVFile  getSUBTITLEExtensions )com.tvplayer.webdav.data.model.WebDAVFile  getSubstringAfterLast )com.tvplayer.webdav.data.model.WebDAVFile  getSubtitleExtensions )com.tvplayer.webdav.data.model.WebDAVFile  getVIDEOExtensions )com.tvplayer.webdav.data.model.WebDAVFile  getVideoExtensions )com.tvplayer.webdav.data.model.WebDAVFile  isDirectory )com.tvplayer.webdav.data.model.WebDAVFile  	lowercase )com.tvplayer.webdav.data.model.WebDAVFile  name )com.tvplayer.webdav.data.model.WebDAVFile  setOf )com.tvplayer.webdav.data.model.WebDAVFile  substringAfterLast )com.tvplayer.webdav.data.model.WebDAVFile  subtitleExtensions )com.tvplayer.webdav.data.model.WebDAVFile  videoExtensions )com.tvplayer.webdav.data.model.WebDAVFile  Boolean 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  Date 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  Long 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  String 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  audioExtensions 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  getLOWERCASE 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  getLowercase 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  getSETOf 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  getSUBSTRINGAfterLast 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  getSetOf 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  getSubstringAfterLast 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  	lowercase 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  setOf 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  substringAfterLast 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  subtitleExtensions 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  videoExtensions 3com.tvplayer.webdav.data.model.WebDAVFile.Companion  Boolean +com.tvplayer.webdav.data.model.WebDAVServer  Long +com.tvplayer.webdav.data.model.WebDAVServer  String +com.tvplayer.webdav.data.model.WebDAVServer  Boolean  com.tvplayer.webdav.data.scanner  Int  com.tvplayer.webdav.data.scanner  List  com.tvplayer.webdav.data.scanner  MediaScanner  com.tvplayer.webdav.data.scanner  Pattern  com.tvplayer.webdav.data.scanner  String  com.tvplayer.webdav.data.scanner  listOf  com.tvplayer.webdav.data.scanner  setOf  com.tvplayer.webdav.data.scanner  Boolean -com.tvplayer.webdav.data.scanner.MediaScanner  Inject -com.tvplayer.webdav.data.scanner.MediaScanner  Int -com.tvplayer.webdav.data.scanner.MediaScanner  List -com.tvplayer.webdav.data.scanner.MediaScanner  	MediaItem -com.tvplayer.webdav.data.scanner.MediaScanner  Pattern -com.tvplayer.webdav.data.scanner.MediaScanner  ScanProgressCallback -com.tvplayer.webdav.data.scanner.MediaScanner  SimpleWebDAVClient -com.tvplayer.webdav.data.scanner.MediaScanner  String -com.tvplayer.webdav.data.scanner.MediaScanner  
TmdbClient -com.tvplayer.webdav.data.scanner.MediaScanner  
WebDAVFile -com.tvplayer.webdav.data.scanner.MediaScanner  listOf -com.tvplayer.webdav.data.scanner.MediaScanner  setOf -com.tvplayer.webdav.data.scanner.MediaScanner  Boolean 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  Inject 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  Int 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  List 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  	MediaItem 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  Pattern 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  SimpleWebDAVClient 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  String 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  
TmdbClient 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  
WebDAVFile 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  	getLISTOf 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  	getListOf 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  getSETOf 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  getSetOf 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  listOf 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  setOf 7com.tvplayer.webdav.data.scanner.MediaScanner.Companion  Int Bcom.tvplayer.webdav.data.scanner.MediaScanner.ScanProgressCallback  List Bcom.tvplayer.webdav.data.scanner.MediaScanner.ScanProgressCallback  	MediaItem Bcom.tvplayer.webdav.data.scanner.MediaScanner.ScanProgressCallback  String Bcom.tvplayer.webdav.data.scanner.MediaScanner.ScanProgressCallback  Boolean com.tvplayer.webdav.data.tmdb  Float com.tvplayer.webdav.data.tmdb  Int com.tvplayer.webdav.data.tmdb  List com.tvplayer.webdav.data.tmdb  Locale com.tvplayer.webdav.data.tmdb  Long com.tvplayer.webdav.data.tmdb  SimpleDateFormat com.tvplayer.webdav.data.tmdb  String com.tvplayer.webdav.data.tmdb  TmdbApiService com.tvplayer.webdav.data.tmdb  
TmdbClient com.tvplayer.webdav.data.tmdb  TmdbConfiguration com.tvplayer.webdav.data.tmdb  TmdbEpisode com.tvplayer.webdav.data.tmdb  TmdbErrorResponse com.tvplayer.webdav.data.tmdb  	TmdbGenre com.tvplayer.webdav.data.tmdb  TmdbImageConfiguration com.tvplayer.webdav.data.tmdb  	TmdbMovie com.tvplayer.webdav.data.tmdb  TmdbMovieSearchResponse com.tvplayer.webdav.data.tmdb  
TmdbSeason com.tvplayer.webdav.data.tmdb  TmdbSeasonDetails com.tvplayer.webdav.data.tmdb  TmdbTVSearchResponse com.tvplayer.webdav.data.tmdb  
TmdbTVShow com.tvplayer.webdav.data.tmdb  Boolean ,com.tvplayer.webdav.data.tmdb.TmdbApiService  GET ,com.tvplayer.webdav.data.tmdb.TmdbApiService  Int ,com.tvplayer.webdav.data.tmdb.TmdbApiService  Path ,com.tvplayer.webdav.data.tmdb.TmdbApiService  Query ,com.tvplayer.webdav.data.tmdb.TmdbApiService  Response ,com.tvplayer.webdav.data.tmdb.TmdbApiService  String ,com.tvplayer.webdav.data.tmdb.TmdbApiService  TmdbConfiguration ,com.tvplayer.webdav.data.tmdb.TmdbApiService  	TmdbMovie ,com.tvplayer.webdav.data.tmdb.TmdbApiService  TmdbMovieSearchResponse ,com.tvplayer.webdav.data.tmdb.TmdbApiService  TmdbSeasonDetails ,com.tvplayer.webdav.data.tmdb.TmdbApiService  TmdbTVSearchResponse ,com.tvplayer.webdav.data.tmdb.TmdbApiService  
TmdbTVShow ,com.tvplayer.webdav.data.tmdb.TmdbApiService  Boolean 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  GET 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  Int 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  Path 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  Query 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  Response 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  String 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  TmdbConfiguration 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  	TmdbMovie 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  TmdbMovieSearchResponse 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  TmdbSeasonDetails 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  TmdbTVSearchResponse 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  
TmdbTVShow 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  Inject (com.tvplayer.webdav.data.tmdb.TmdbClient  Int (com.tvplayer.webdav.data.tmdb.TmdbClient  Locale (com.tvplayer.webdav.data.tmdb.TmdbClient  Long (com.tvplayer.webdav.data.tmdb.TmdbClient  	MediaItem (com.tvplayer.webdav.data.tmdb.TmdbClient  SimpleDateFormat (com.tvplayer.webdav.data.tmdb.TmdbClient  String (com.tvplayer.webdav.data.tmdb.TmdbClient  TmdbApiService (com.tvplayer.webdav.data.tmdb.TmdbClient  	TmdbMovie (com.tvplayer.webdav.data.tmdb.TmdbClient  
TmdbTVShow (com.tvplayer.webdav.data.tmdb.TmdbClient  Inject 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  Int 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  Locale 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  Long 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  	MediaItem 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  SimpleDateFormat 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  String 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  TmdbApiService 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  	TmdbMovie 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  
TmdbTVShow 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  TmdbImageConfiguration /com.tvplayer.webdav.data.tmdb.TmdbConfiguration  Float )com.tvplayer.webdav.data.tmdb.TmdbEpisode  Int )com.tvplayer.webdav.data.tmdb.TmdbEpisode  SerializedName )com.tvplayer.webdav.data.tmdb.TmdbEpisode  String )com.tvplayer.webdav.data.tmdb.TmdbEpisode  Int /com.tvplayer.webdav.data.tmdb.TmdbErrorResponse  SerializedName /com.tvplayer.webdav.data.tmdb.TmdbErrorResponse  String /com.tvplayer.webdav.data.tmdb.TmdbErrorResponse  Int 'com.tvplayer.webdav.data.tmdb.TmdbGenre  String 'com.tvplayer.webdav.data.tmdb.TmdbGenre  List 4com.tvplayer.webdav.data.tmdb.TmdbImageConfiguration  SerializedName 4com.tvplayer.webdav.data.tmdb.TmdbImageConfiguration  String 4com.tvplayer.webdav.data.tmdb.TmdbImageConfiguration  Float 'com.tvplayer.webdav.data.tmdb.TmdbMovie  Int 'com.tvplayer.webdav.data.tmdb.TmdbMovie  List 'com.tvplayer.webdav.data.tmdb.TmdbMovie  SerializedName 'com.tvplayer.webdav.data.tmdb.TmdbMovie  String 'com.tvplayer.webdav.data.tmdb.TmdbMovie  	TmdbGenre 'com.tvplayer.webdav.data.tmdb.TmdbMovie  Int 5com.tvplayer.webdav.data.tmdb.TmdbMovieSearchResponse  List 5com.tvplayer.webdav.data.tmdb.TmdbMovieSearchResponse  SerializedName 5com.tvplayer.webdav.data.tmdb.TmdbMovieSearchResponse  	TmdbMovie 5com.tvplayer.webdav.data.tmdb.TmdbMovieSearchResponse  Int (com.tvplayer.webdav.data.tmdb.TmdbSeason  SerializedName (com.tvplayer.webdav.data.tmdb.TmdbSeason  String (com.tvplayer.webdav.data.tmdb.TmdbSeason  Int /com.tvplayer.webdav.data.tmdb.TmdbSeasonDetails  List /com.tvplayer.webdav.data.tmdb.TmdbSeasonDetails  SerializedName /com.tvplayer.webdav.data.tmdb.TmdbSeasonDetails  String /com.tvplayer.webdav.data.tmdb.TmdbSeasonDetails  TmdbEpisode /com.tvplayer.webdav.data.tmdb.TmdbSeasonDetails  Int 2com.tvplayer.webdav.data.tmdb.TmdbTVSearchResponse  List 2com.tvplayer.webdav.data.tmdb.TmdbTVSearchResponse  SerializedName 2com.tvplayer.webdav.data.tmdb.TmdbTVSearchResponse  
TmdbTVShow 2com.tvplayer.webdav.data.tmdb.TmdbTVSearchResponse  Float (com.tvplayer.webdav.data.tmdb.TmdbTVShow  Int (com.tvplayer.webdav.data.tmdb.TmdbTVShow  List (com.tvplayer.webdav.data.tmdb.TmdbTVShow  SerializedName (com.tvplayer.webdav.data.tmdb.TmdbTVShow  String (com.tvplayer.webdav.data.tmdb.TmdbTVShow  	TmdbGenre (com.tvplayer.webdav.data.tmdb.TmdbTVShow  
TmdbSeason (com.tvplayer.webdav.data.tmdb.TmdbTVShow  Boolean com.tvplayer.webdav.data.webdav  Date com.tvplayer.webdav.data.webdav  List com.tvplayer.webdav.data.webdav  OkHttpClient com.tvplayer.webdav.data.webdav  RequestBody com.tvplayer.webdav.data.webdav  Result com.tvplayer.webdav.data.webdav  SimpleWebDAVClient com.tvplayer.webdav.data.webdav  String com.tvplayer.webdav.data.webdav  Boolean 2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient  Date 2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient  Inject 2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient  List 2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient  OkHttpClient 2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient  RequestBody 2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient  Result 2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient  String 2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient  
WebDAVFile 2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient  WebDAVServer 2com.tvplayer.webdav.data.webdav.SimpleWebDAVClient  AnnotationRetention com.tvplayer.webdav.di  	Retention com.tvplayer.webdav.di  SingletonComponent com.tvplayer.webdav.di  
TmdbClient com.tvplayer.webdav.di  WebDAVClient com.tvplayer.webdav.di  WebDAVModule com.tvplayer.webdav.di  OkHttpClient #com.tvplayer.webdav.di.WebDAVModule  Provides #com.tvplayer.webdav.di.WebDAVModule  Retrofit #com.tvplayer.webdav.di.WebDAVModule  SimpleWebDAVClient #com.tvplayer.webdav.di.WebDAVModule  	Singleton #com.tvplayer.webdav.di.WebDAVModule  TmdbApiService #com.tvplayer.webdav.di.WebDAVModule  
TmdbClient #com.tvplayer.webdav.di.WebDAVModule  WebDAVClient #com.tvplayer.webdav.di.WebDAVModule  Boolean com.tvplayer.webdav.ui.home  CategoryAdapter com.tvplayer.webdav.ui.home  HomeFragment com.tvplayer.webdav.ui.home  
HomeViewModel com.tvplayer.webdav.ui.home  Int com.tvplayer.webdav.ui.home  List com.tvplayer.webdav.ui.home  MediaPosterAdapter com.tvplayer.webdav.ui.home  MutableLiveData com.tvplayer.webdav.ui.home  R com.tvplayer.webdav.ui.home  String com.tvplayer.webdav.ui.home  Unit com.tvplayer.webdav.ui.home  com com.tvplayer.webdav.ui.home  getValue com.tvplayer.webdav.ui.home  invoke com.tvplayer.webdav.ui.home  provideDelegate com.tvplayer.webdav.ui.home  
viewModels com.tvplayer.webdav.ui.home  Boolean +com.tvplayer.webdav.ui.home.CategoryAdapter  CategoryDiffCallback +com.tvplayer.webdav.ui.home.CategoryAdapter  CategoryViewHolder +com.tvplayer.webdav.ui.home.CategoryAdapter  DiffUtil +com.tvplayer.webdav.ui.home.CategoryAdapter  	ImageView +com.tvplayer.webdav.ui.home.CategoryAdapter  Int +com.tvplayer.webdav.ui.home.CategoryAdapter  
MediaCategory +com.tvplayer.webdav.ui.home.CategoryAdapter  R +com.tvplayer.webdav.ui.home.CategoryAdapter  RecyclerView +com.tvplayer.webdav.ui.home.CategoryAdapter  TextView +com.tvplayer.webdav.ui.home.CategoryAdapter  Unit +com.tvplayer.webdav.ui.home.CategoryAdapter  View +com.tvplayer.webdav.ui.home.CategoryAdapter  	ViewGroup +com.tvplayer.webdav.ui.home.CategoryAdapter  Boolean @com.tvplayer.webdav.ui.home.CategoryAdapter.CategoryDiffCallback  
MediaCategory @com.tvplayer.webdav.ui.home.CategoryAdapter.CategoryDiffCallback  	ImageView >com.tvplayer.webdav.ui.home.CategoryAdapter.CategoryViewHolder  
MediaCategory >com.tvplayer.webdav.ui.home.CategoryAdapter.CategoryViewHolder  R >com.tvplayer.webdav.ui.home.CategoryAdapter.CategoryViewHolder  TextView >com.tvplayer.webdav.ui.home.CategoryAdapter.CategoryViewHolder  Unit >com.tvplayer.webdav.ui.home.CategoryAdapter.CategoryViewHolder  View >com.tvplayer.webdav.ui.home.CategoryAdapter.CategoryViewHolder  Bundle (com.tvplayer.webdav.ui.home.HomeFragment  CategoryAdapter (com.tvplayer.webdav.ui.home.HomeFragment  HomeFragment (com.tvplayer.webdav.ui.home.HomeFragment  
HomeViewModel (com.tvplayer.webdav.ui.home.HomeFragment  LayoutInflater (com.tvplayer.webdav.ui.home.HomeFragment  
MediaCategory (com.tvplayer.webdav.ui.home.HomeFragment  MediaPosterAdapter (com.tvplayer.webdav.ui.home.HomeFragment  RecyclerView (com.tvplayer.webdav.ui.home.HomeFragment  View (com.tvplayer.webdav.ui.home.HomeFragment  	ViewGroup (com.tvplayer.webdav.ui.home.HomeFragment  com (com.tvplayer.webdav.ui.home.HomeFragment  getGETValue (com.tvplayer.webdav.ui.home.HomeFragment  getGetValue (com.tvplayer.webdav.ui.home.HomeFragment  getPROVIDEDelegate (com.tvplayer.webdav.ui.home.HomeFragment  getProvideDelegate (com.tvplayer.webdav.ui.home.HomeFragment  
getVIEWModels (com.tvplayer.webdav.ui.home.HomeFragment  getValue (com.tvplayer.webdav.ui.home.HomeFragment  
getViewModels (com.tvplayer.webdav.ui.home.HomeFragment  invoke (com.tvplayer.webdav.ui.home.HomeFragment  provideDelegate (com.tvplayer.webdav.ui.home.HomeFragment  
viewModels (com.tvplayer.webdav.ui.home.HomeFragment  Bundle 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  CategoryAdapter 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  HomeFragment 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  
HomeViewModel 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  LayoutInflater 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  
MediaCategory 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  MediaPosterAdapter 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  RecyclerView 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  View 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  	ViewGroup 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  com 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  getGETValue 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  getGetValue 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  getPROVIDEDelegate 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  getProvideDelegate 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  getValue 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  invoke 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  provideDelegate 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  
viewModels 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  Boolean )com.tvplayer.webdav.ui.home.HomeViewModel  Inject )com.tvplayer.webdav.ui.home.HomeViewModel  List )com.tvplayer.webdav.ui.home.HomeViewModel  LiveData )com.tvplayer.webdav.ui.home.HomeViewModel  
MediaCategory )com.tvplayer.webdav.ui.home.HomeViewModel  	MediaItem )com.tvplayer.webdav.ui.home.HomeViewModel  MutableLiveData )com.tvplayer.webdav.ui.home.HomeViewModel  SimpleWebDAVClient )com.tvplayer.webdav.ui.home.HomeViewModel  String )com.tvplayer.webdav.ui.home.HomeViewModel  _categories )com.tvplayer.webdav.ui.home.HomeViewModel  _continueWatching )com.tvplayer.webdav.ui.home.HomeViewModel  _error )com.tvplayer.webdav.ui.home.HomeViewModel  
_isLoading )com.tvplayer.webdav.ui.home.HomeViewModel  _movies )com.tvplayer.webdav.ui.home.HomeViewModel  _recentlyAdded )com.tvplayer.webdav.ui.home.HomeViewModel  _tvShows )com.tvplayer.webdav.ui.home.HomeViewModel  Boolean .com.tvplayer.webdav.ui.home.MediaPosterAdapter  DiffUtil .com.tvplayer.webdav.ui.home.MediaPosterAdapter  	ImageView .com.tvplayer.webdav.ui.home.MediaPosterAdapter  Int .com.tvplayer.webdav.ui.home.MediaPosterAdapter  MediaDiffCallback .com.tvplayer.webdav.ui.home.MediaPosterAdapter  	MediaItem .com.tvplayer.webdav.ui.home.MediaPosterAdapter  MediaViewHolder .com.tvplayer.webdav.ui.home.MediaPosterAdapter  ProgressBar .com.tvplayer.webdav.ui.home.MediaPosterAdapter  R .com.tvplayer.webdav.ui.home.MediaPosterAdapter  RecyclerView .com.tvplayer.webdav.ui.home.MediaPosterAdapter  TextView .com.tvplayer.webdav.ui.home.MediaPosterAdapter  Unit .com.tvplayer.webdav.ui.home.MediaPosterAdapter  View .com.tvplayer.webdav.ui.home.MediaPosterAdapter  	ViewGroup .com.tvplayer.webdav.ui.home.MediaPosterAdapter  Boolean @com.tvplayer.webdav.ui.home.MediaPosterAdapter.MediaDiffCallback  	MediaItem @com.tvplayer.webdav.ui.home.MediaPosterAdapter.MediaDiffCallback  	ImageView >com.tvplayer.webdav.ui.home.MediaPosterAdapter.MediaViewHolder  	MediaItem >com.tvplayer.webdav.ui.home.MediaPosterAdapter.MediaViewHolder  ProgressBar >com.tvplayer.webdav.ui.home.MediaPosterAdapter.MediaViewHolder  R >com.tvplayer.webdav.ui.home.MediaPosterAdapter.MediaViewHolder  TextView >com.tvplayer.webdav.ui.home.MediaPosterAdapter.MediaViewHolder  Unit >com.tvplayer.webdav.ui.home.MediaPosterAdapter.MediaViewHolder  View >com.tvplayer.webdav.ui.home.MediaPosterAdapter.MediaViewHolder  Any com.tvplayer.webdav.ui.main  Boolean com.tvplayer.webdav.ui.main  
CardPresenter com.tvplayer.webdav.ui.main  	Delegates com.tvplayer.webdav.ui.main  Int com.tvplayer.webdav.ui.main  MainActivity com.tvplayer.webdav.ui.main  MainFragment com.tvplayer.webdav.ui.main  provideDelegate com.tvplayer.webdav.ui.main  Any )com.tvplayer.webdav.ui.main.CardPresenter  Boolean )com.tvplayer.webdav.ui.main.CardPresenter  	Delegates )com.tvplayer.webdav.ui.main.CardPresenter  Drawable )com.tvplayer.webdav.ui.main.CardPresenter  
ImageCardView )com.tvplayer.webdav.ui.main.CardPresenter  Int )com.tvplayer.webdav.ui.main.CardPresenter  	Presenter )com.tvplayer.webdav.ui.main.CardPresenter  	ViewGroup )com.tvplayer.webdav.ui.main.CardPresenter  getPROVIDEDelegate )com.tvplayer.webdav.ui.main.CardPresenter  getProvideDelegate )com.tvplayer.webdav.ui.main.CardPresenter  provideDelegate )com.tvplayer.webdav.ui.main.CardPresenter  Any 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  Boolean 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  	Delegates 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  Drawable 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  
ImageCardView 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  Int 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  	Presenter 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  	ViewGroup 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  getPROVIDEDelegate 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  getProvideDelegate 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  provideDelegate 3com.tvplayer.webdav.ui.main.CardPresenter.Companion  Bundle (com.tvplayer.webdav.ui.main.MainActivity  Bundle (com.tvplayer.webdav.ui.main.MainFragment  LayoutInflater (com.tvplayer.webdav.ui.main.MainFragment  View (com.tvplayer.webdav.ui.main.MainFragment  	ViewGroup (com.tvplayer.webdav.ui.main.MainFragment  Boolean com.tvplayer.webdav.ui.scanner  List com.tvplayer.webdav.ui.scanner  MutableLiveData com.tvplayer.webdav.ui.scanner  Result com.tvplayer.webdav.ui.scanner  ScannerFragment com.tvplayer.webdav.ui.scanner  ScannerViewModel com.tvplayer.webdav.ui.scanner  String com.tvplayer.webdav.ui.scanner  getValue com.tvplayer.webdav.ui.scanner  invoke com.tvplayer.webdav.ui.scanner  provideDelegate com.tvplayer.webdav.ui.scanner  
viewModels com.tvplayer.webdav.ui.scanner  Bundle .com.tvplayer.webdav.ui.scanner.ScannerFragment  Button .com.tvplayer.webdav.ui.scanner.ScannerFragment  LayoutInflater .com.tvplayer.webdav.ui.scanner.ScannerFragment  ProgressBar .com.tvplayer.webdav.ui.scanner.ScannerFragment  ScannerFragment .com.tvplayer.webdav.ui.scanner.ScannerFragment  ScannerViewModel .com.tvplayer.webdav.ui.scanner.ScannerFragment  TextView .com.tvplayer.webdav.ui.scanner.ScannerFragment  View .com.tvplayer.webdav.ui.scanner.ScannerFragment  	ViewGroup .com.tvplayer.webdav.ui.scanner.ScannerFragment  getGETValue .com.tvplayer.webdav.ui.scanner.ScannerFragment  getGetValue .com.tvplayer.webdav.ui.scanner.ScannerFragment  getPROVIDEDelegate .com.tvplayer.webdav.ui.scanner.ScannerFragment  getProvideDelegate .com.tvplayer.webdav.ui.scanner.ScannerFragment  
getVIEWModels .com.tvplayer.webdav.ui.scanner.ScannerFragment  getValue .com.tvplayer.webdav.ui.scanner.ScannerFragment  
getViewModels .com.tvplayer.webdav.ui.scanner.ScannerFragment  invoke .com.tvplayer.webdav.ui.scanner.ScannerFragment  provideDelegate .com.tvplayer.webdav.ui.scanner.ScannerFragment  
viewModels .com.tvplayer.webdav.ui.scanner.ScannerFragment  Bundle 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  Button 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  LayoutInflater 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  ProgressBar 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  ScannerFragment 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  ScannerViewModel 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  TextView 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  View 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  	ViewGroup 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  getGETValue 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  getGetValue 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  getPROVIDEDelegate 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  getProvideDelegate 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  getValue 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  invoke 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  provideDelegate 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  
viewModels 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  Boolean /com.tvplayer.webdav.ui.scanner.ScannerViewModel  Inject /com.tvplayer.webdav.ui.scanner.ScannerViewModel  List /com.tvplayer.webdav.ui.scanner.ScannerViewModel  LiveData /com.tvplayer.webdav.ui.scanner.ScannerViewModel  	MediaItem /com.tvplayer.webdav.ui.scanner.ScannerViewModel  MediaScanner /com.tvplayer.webdav.ui.scanner.ScannerViewModel  MutableLiveData /com.tvplayer.webdav.ui.scanner.ScannerViewModel  Result /com.tvplayer.webdav.ui.scanner.ScannerViewModel  String /com.tvplayer.webdav.ui.scanner.ScannerViewModel  _isScanning /com.tvplayer.webdav.ui.scanner.ScannerViewModel  
_scanProgress /com.tvplayer.webdav.ui.scanner.ScannerViewModel  _scanResult /com.tvplayer.webdav.ui.scanner.ScannerViewModel  _scanStatus /com.tvplayer.webdav.ui.scanner.ScannerViewModel  
_selectedPath /com.tvplayer.webdav.ui.scanner.ScannerViewModel  SettingsFragment com.tvplayer.webdav.ui.settings  invoke com.tvplayer.webdav.ui.settings  Bundle 0com.tvplayer.webdav.ui.settings.SettingsFragment  LayoutInflater 0com.tvplayer.webdav.ui.settings.SettingsFragment  SettingsFragment 0com.tvplayer.webdav.ui.settings.SettingsFragment  View 0com.tvplayer.webdav.ui.settings.SettingsFragment  	ViewGroup 0com.tvplayer.webdav.ui.settings.SettingsFragment  invoke 0com.tvplayer.webdav.ui.settings.SettingsFragment  Bundle :com.tvplayer.webdav.ui.settings.SettingsFragment.Companion  LayoutInflater :com.tvplayer.webdav.ui.settings.SettingsFragment.Companion  SettingsFragment :com.tvplayer.webdav.ui.settings.SettingsFragment.Companion  View :com.tvplayer.webdav.ui.settings.SettingsFragment.Companion  	ViewGroup :com.tvplayer.webdav.ui.settings.SettingsFragment.Companion  invoke :com.tvplayer.webdav.ui.settings.SettingsFragment.Companion  Boolean com.tvplayer.webdav.ui.webdav  MutableLiveData com.tvplayer.webdav.ui.webdav  Result com.tvplayer.webdav.ui.webdav  String com.tvplayer.webdav.ui.webdav  WebDAVConnectionFragment com.tvplayer.webdav.ui.webdav  WebDAVConnectionViewModel com.tvplayer.webdav.ui.webdav  getValue com.tvplayer.webdav.ui.webdav  invoke com.tvplayer.webdav.ui.webdav  provideDelegate com.tvplayer.webdav.ui.webdav  
viewModels com.tvplayer.webdav.ui.webdav  Bundle 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  Button 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  EditText 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  LayoutInflater 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  TextView 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  View 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  	ViewGroup 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  WebDAVConnectionFragment 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  WebDAVConnectionViewModel 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  WebDAVServer 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  getGETValue 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  getGetValue 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  getPROVIDEDelegate 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  getProvideDelegate 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  
getVIEWModels 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  getValue 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  
getViewModels 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  invoke 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  provideDelegate 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  
viewModels 6com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment  Bundle @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  Button @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  EditText @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  LayoutInflater @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  TextView @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  View @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  	ViewGroup @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  WebDAVConnectionFragment @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  WebDAVConnectionViewModel @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  WebDAVServer @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  getGETValue @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  getGetValue @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  getPROVIDEDelegate @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  getProvideDelegate @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  getValue @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  invoke @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  provideDelegate @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  
viewModels @com.tvplayer.webdav.ui.webdav.WebDAVConnectionFragment.Companion  Boolean 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  Inject 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  LiveData 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  MutableLiveData 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  Result 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  SimpleWebDAVClient 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  String 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  WebDAVServer 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  _connectionResult 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  _connectionStatus 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  _currentServer 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  
_isLoading 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  SingletonComponent dagger.hilt.components  IOException java.io  AnnotationRetention 	java.lang  	Delegates 	java.lang  HomeFragment 	java.lang  Locale 	java.lang  MutableLiveData 	java.lang  Pattern 	java.lang  R 	java.lang  ScannerFragment 	java.lang  SettingsFragment 	java.lang  SimpleDateFormat 	java.lang  SingletonComponent 	java.lang  WebDAVConnectionFragment 	java.lang  audioExtensions 	java.lang  com 	java.lang  getValue 	java.lang  listOf 	java.lang  	lowercase 	java.lang  provideDelegate 	java.lang  setOf 	java.lang  substringAfterLast 	java.lang  subtitleExtensions 	java.lang  videoExtensions 	java.lang  SimpleDateFormat 	java.text  Date 	java.util  Locale 	java.util  OkHttpClient 	java.util  RequestBody 	java.util  Result 	java.util  SimpleDateFormat 	java.util  
getDefault java.util.Locale  TimeUnit java.util.concurrent  Pattern java.util.regex  CASE_INSENSITIVE java.util.regex.Pattern  compile java.util.regex.Pattern  Inject javax.inject  	Qualifier javax.inject  	Singleton javax.inject  AnnotationRetention kotlin  Any kotlin  Boolean kotlin  Char kotlin  	Delegates kotlin  Float kotlin  HomeFragment kotlin  Int kotlin  Lazy kotlin  Locale kotlin  Long kotlin  MutableLiveData kotlin  Nothing kotlin  Pattern kotlin  R kotlin  Result kotlin  ScannerFragment kotlin  SettingsFragment kotlin  SimpleDateFormat kotlin  SingletonComponent kotlin  String kotlin  Unit kotlin  WebDAVConnectionFragment kotlin  audioExtensions kotlin  com kotlin  getValue kotlin  listOf kotlin  	lowercase kotlin  provideDelegate kotlin  setOf kotlin  substringAfterLast kotlin  subtitleExtensions kotlin  videoExtensions kotlin  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getLOWERCASE 
kotlin.String  getLowercase 
kotlin.String  getSUBSTRINGAfterLast 
kotlin.String  getSubstringAfterLast 
kotlin.String  AnnotationRetention kotlin.annotation  	Delegates kotlin.annotation  HomeFragment kotlin.annotation  Locale kotlin.annotation  MutableLiveData kotlin.annotation  Pattern kotlin.annotation  R kotlin.annotation  Result kotlin.annotation  	Retention kotlin.annotation  ScannerFragment kotlin.annotation  SettingsFragment kotlin.annotation  SimpleDateFormat kotlin.annotation  SingletonComponent kotlin.annotation  WebDAVConnectionFragment kotlin.annotation  audioExtensions kotlin.annotation  com kotlin.annotation  getValue kotlin.annotation  listOf kotlin.annotation  	lowercase kotlin.annotation  provideDelegate kotlin.annotation  setOf kotlin.annotation  substringAfterLast kotlin.annotation  subtitleExtensions kotlin.annotation  videoExtensions kotlin.annotation  BINARY %kotlin.annotation.AnnotationRetention  AnnotationRetention kotlin.collections  	Delegates kotlin.collections  HomeFragment kotlin.collections  List kotlin.collections  Locale kotlin.collections  MutableLiveData kotlin.collections  Pattern kotlin.collections  R kotlin.collections  Result kotlin.collections  ScannerFragment kotlin.collections  Set kotlin.collections  SettingsFragment kotlin.collections  SimpleDateFormat kotlin.collections  SingletonComponent kotlin.collections  WebDAVConnectionFragment kotlin.collections  audioExtensions kotlin.collections  com kotlin.collections  getValue kotlin.collections  listOf kotlin.collections  	lowercase kotlin.collections  provideDelegate kotlin.collections  setOf kotlin.collections  substringAfterLast kotlin.collections  subtitleExtensions kotlin.collections  videoExtensions kotlin.collections  AnnotationRetention kotlin.comparisons  	Delegates kotlin.comparisons  HomeFragment kotlin.comparisons  Locale kotlin.comparisons  MutableLiveData kotlin.comparisons  Pattern kotlin.comparisons  R kotlin.comparisons  Result kotlin.comparisons  ScannerFragment kotlin.comparisons  SettingsFragment kotlin.comparisons  SimpleDateFormat kotlin.comparisons  SingletonComponent kotlin.comparisons  WebDAVConnectionFragment kotlin.comparisons  audioExtensions kotlin.comparisons  com kotlin.comparisons  getValue kotlin.comparisons  listOf kotlin.comparisons  	lowercase kotlin.comparisons  provideDelegate kotlin.comparisons  setOf kotlin.comparisons  substringAfterLast kotlin.comparisons  subtitleExtensions kotlin.comparisons  videoExtensions kotlin.comparisons  AnnotationRetention 	kotlin.io  	Delegates 	kotlin.io  HomeFragment 	kotlin.io  Locale 	kotlin.io  MutableLiveData 	kotlin.io  Pattern 	kotlin.io  R 	kotlin.io  Result 	kotlin.io  ScannerFragment 	kotlin.io  SettingsFragment 	kotlin.io  SimpleDateFormat 	kotlin.io  SingletonComponent 	kotlin.io  WebDAVConnectionFragment 	kotlin.io  audioExtensions 	kotlin.io  com 	kotlin.io  getValue 	kotlin.io  listOf 	kotlin.io  	lowercase 	kotlin.io  provideDelegate 	kotlin.io  setOf 	kotlin.io  substringAfterLast 	kotlin.io  subtitleExtensions 	kotlin.io  videoExtensions 	kotlin.io  AnnotationRetention 
kotlin.jvm  	Delegates 
kotlin.jvm  HomeFragment 
kotlin.jvm  Locale 
kotlin.jvm  MutableLiveData 
kotlin.jvm  Pattern 
kotlin.jvm  R 
kotlin.jvm  Result 
kotlin.jvm  ScannerFragment 
kotlin.jvm  SettingsFragment 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  SingletonComponent 
kotlin.jvm  WebDAVConnectionFragment 
kotlin.jvm  audioExtensions 
kotlin.jvm  com 
kotlin.jvm  getValue 
kotlin.jvm  listOf 
kotlin.jvm  	lowercase 
kotlin.jvm  provideDelegate 
kotlin.jvm  setOf 
kotlin.jvm  substringAfterLast 
kotlin.jvm  subtitleExtensions 
kotlin.jvm  videoExtensions 
kotlin.jvm  	Delegates kotlin.properties  ReadWriteProperty kotlin.properties  notNull kotlin.properties.Delegates  getPROVIDEDelegate #kotlin.properties.ReadWriteProperty  getProvideDelegate #kotlin.properties.ReadWriteProperty  getValue #kotlin.properties.ReadWriteProperty  provideDelegate #kotlin.properties.ReadWriteProperty  setValue #kotlin.properties.ReadWriteProperty  AnnotationRetention 
kotlin.ranges  	Delegates 
kotlin.ranges  HomeFragment 
kotlin.ranges  Locale 
kotlin.ranges  MutableLiveData 
kotlin.ranges  Pattern 
kotlin.ranges  R 
kotlin.ranges  Result 
kotlin.ranges  ScannerFragment 
kotlin.ranges  SettingsFragment 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  SingletonComponent 
kotlin.ranges  WebDAVConnectionFragment 
kotlin.ranges  audioExtensions 
kotlin.ranges  com 
kotlin.ranges  getValue 
kotlin.ranges  listOf 
kotlin.ranges  	lowercase 
kotlin.ranges  provideDelegate 
kotlin.ranges  setOf 
kotlin.ranges  substringAfterLast 
kotlin.ranges  subtitleExtensions 
kotlin.ranges  videoExtensions 
kotlin.ranges  KClass kotlin.reflect  AnnotationRetention kotlin.sequences  	Delegates kotlin.sequences  HomeFragment kotlin.sequences  Locale kotlin.sequences  MutableLiveData kotlin.sequences  Pattern kotlin.sequences  R kotlin.sequences  Result kotlin.sequences  ScannerFragment kotlin.sequences  SettingsFragment kotlin.sequences  SimpleDateFormat kotlin.sequences  SingletonComponent kotlin.sequences  WebDAVConnectionFragment kotlin.sequences  audioExtensions kotlin.sequences  com kotlin.sequences  getValue kotlin.sequences  listOf kotlin.sequences  	lowercase kotlin.sequences  provideDelegate kotlin.sequences  setOf kotlin.sequences  substringAfterLast kotlin.sequences  subtitleExtensions kotlin.sequences  videoExtensions kotlin.sequences  AnnotationRetention kotlin.text  	Delegates kotlin.text  HomeFragment kotlin.text  Locale kotlin.text  MutableLiveData kotlin.text  Pattern kotlin.text  R kotlin.text  Result kotlin.text  ScannerFragment kotlin.text  SettingsFragment kotlin.text  SimpleDateFormat kotlin.text  SingletonComponent kotlin.text  WebDAVConnectionFragment kotlin.text  audioExtensions kotlin.text  com kotlin.text  getValue kotlin.text  listOf kotlin.text  	lowercase kotlin.text  provideDelegate kotlin.text  setOf kotlin.text  substringAfterLast kotlin.text  subtitleExtensions kotlin.text  videoExtensions kotlin.text  Dispatchers kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  	Parcelize kotlinx.parcelize  Date okhttp3  	MediaType okhttp3  OkHttpClient okhttp3  RequestBody okhttp3  Result okhttp3  	Companion okhttp3.MediaType  toMediaType okhttp3.MediaType.Companion  	Companion okhttp3.RequestBody  
toRequestBody okhttp3.RequestBody.Companion  HttpLoggingInterceptor okhttp3.logging  Response 	retrofit2  Retrofit 	retrofit2  GsonConverterFactory retrofit2.converter.gson  GET retrofit2.http  Path retrofit2.http  Query retrofit2.http  GridSpacingItemDecoration com.tvplayer.webdav.ui.home  FocusHighlightHelper com.tvplayer.webdav.ui.home  AnimatorSet android.animation  ObjectAnimator android.animation  Rect android.graphics   AccelerateDecelerateInterpolator android.view.animation  ItemDecoration )androidx.recyclerview.widget.RecyclerView  State )androidx.recyclerview.widget.RecyclerView  Boolean 8androidx.recyclerview.widget.RecyclerView.ItemDecoration  Int 8androidx.recyclerview.widget.RecyclerView.ItemDecoration  Rect 8androidx.recyclerview.widget.RecyclerView.ItemDecoration  RecyclerView 8androidx.recyclerview.widget.RecyclerView.ItemDecoration  View 8androidx.recyclerview.widget.RecyclerView.ItemDecoration  Float com.tvplayer.webdav.ui.home  Float 0com.tvplayer.webdav.ui.home.FocusHighlightHelper  View 0com.tvplayer.webdav.ui.home.FocusHighlightHelper  Boolean 5com.tvplayer.webdav.ui.home.GridSpacingItemDecoration  Int 5com.tvplayer.webdav.ui.home.GridSpacingItemDecoration  Rect 5com.tvplayer.webdav.ui.home.GridSpacingItemDecoration  RecyclerView 5com.tvplayer.webdav.ui.home.GridSpacingItemDecoration  View 5com.tvplayer.webdav.ui.home.GridSpacingItemDecoration  
ValueAnimator android.animation  OvershootInterpolator android.view.animation  Boolean 0com.tvplayer.webdav.ui.home.FocusHighlightHelper  iv_play_button com.tvplayer.webdav.R.id  layout_progress com.tvplayer.webdav.R.id  	tv_rating com.tvplayer.webdav.R.id  EdgeItemHelper com.tvplayer.webdav.ui.home  
LayoutManager )androidx.recyclerview.widget.RecyclerView  Boolean *com.tvplayer.webdav.ui.home.EdgeItemHelper  Int *com.tvplayer.webdav.ui.home.EdgeItemHelper  RecyclerView *com.tvplayer.webdav.ui.home.EdgeItemHelper  View *com.tvplayer.webdav.ui.home.EdgeItemHelper  PosterFocusAnimator com.tvplayer.webdav.ui.home  CardView androidx.cardview.widget  Boolean /com.tvplayer.webdav.ui.home.PosterFocusAnimator  CardView /com.tvplayer.webdav.ui.home.PosterFocusAnimator  	ImageView /com.tvplayer.webdav.ui.home.PosterFocusAnimator  TextView /com.tvplayer.webdav.ui.home.PosterFocusAnimator  View /com.tvplayer.webdav.ui.home.PosterFocusAnimator  ScraperActivity com.tvplayer.webdav.ui.scraper  ScrapeProgressCallback -com.tvplayer.webdav.data.scraper.MediaScraper  ActivityScraperBinding com.tvplayer.webdav.databinding  MediaFileParser com.tvplayer.webdav.data.parser  
WebDAVFile -com.tvplayer.webdav.data.webdav.WebDAVScanner  MediaScraper  com.tvplayer.webdav.data.scraper  
ScanResult -com.tvplayer.webdav.data.webdav.WebDAVScanner  
MediaDatabase !com.tvplayer.webdav.data.database  
Converters !com.tvplayer.webdav.data.database  MediaDao !com.tvplayer.webdav.data.database  ScrapeResult -com.tvplayer.webdav.data.scraper.MediaScraper  ParseResult /com.tvplayer.webdav.data.parser.MediaFileParser  
WebDAVScanner com.tvplayer.webdav.data.webdav  ActivityScraperBinding android.app.Activity  MediaScraper android.app.Activity  
WebDAVScanner android.app.Activity  Context android.content  ActivityScraperBinding android.content.Context  MediaScraper android.content.Context  
WebDAVScanner android.content.Context  ActivityScraperBinding android.content.ContextWrapper  MediaScraper android.content.ContextWrapper  
WebDAVScanner android.content.ContextWrapper  Xml android.util  ActivityScraperBinding  android.view.ContextThemeWrapper  MediaScraper  android.view.ContextThemeWrapper  
WebDAVScanner  android.view.ContextThemeWrapper  
viewModels androidx.activity  ActivityScraperBinding #androidx.activity.ComponentActivity  MediaScraper #androidx.activity.ComponentActivity  
WebDAVScanner #androidx.activity.ComponentActivity  AppCompatActivity androidx.appcompat.app  ActivityScraperBinding (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  MediaScraper (androidx.appcompat.app.AppCompatActivity  
WebDAVScanner (androidx.appcompat.app.AppCompatActivity  ActivityScraperBinding #androidx.core.app.ComponentActivity  MediaScraper #androidx.core.app.ComponentActivity  
WebDAVScanner #androidx.core.app.ComponentActivity  ActivityScraperBinding &androidx.fragment.app.FragmentActivity  MediaScraper &androidx.fragment.app.FragmentActivity  
WebDAVScanner &androidx.fragment.app.FragmentActivity  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  Context androidx.room.RoomDatabase  MediaDao androidx.room.RoomDatabase  
MediaDatabase androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  Boolean !com.tvplayer.webdav.data.database  Dao !com.tvplayer.webdav.data.database  Date !com.tvplayer.webdav.data.database  Delete !com.tvplayer.webdav.data.database  Insert !com.tvplayer.webdav.data.database  Int !com.tvplayer.webdav.data.database  List !com.tvplayer.webdav.data.database  Long !com.tvplayer.webdav.data.database  	MediaItem !com.tvplayer.webdav.data.database  OnConflictStrategy !com.tvplayer.webdav.data.database  Query !com.tvplayer.webdav.data.database  String !com.tvplayer.webdav.data.database  Update !com.tvplayer.webdav.data.database  Volatile !com.tvplayer.webdav.data.database  Date ,com.tvplayer.webdav.data.database.Converters  Long ,com.tvplayer.webdav.data.database.Converters  
TypeConverter ,com.tvplayer.webdav.data.database.Converters  Boolean *com.tvplayer.webdav.data.database.MediaDao  Delete *com.tvplayer.webdav.data.database.MediaDao  Flow *com.tvplayer.webdav.data.database.MediaDao  Insert *com.tvplayer.webdav.data.database.MediaDao  Int *com.tvplayer.webdav.data.database.MediaDao  List *com.tvplayer.webdav.data.database.MediaDao  Long *com.tvplayer.webdav.data.database.MediaDao  	MediaItem *com.tvplayer.webdav.data.database.MediaDao  OnConflictStrategy *com.tvplayer.webdav.data.database.MediaDao  Query *com.tvplayer.webdav.data.database.MediaDao  String *com.tvplayer.webdav.data.database.MediaDao  Update *com.tvplayer.webdav.data.database.MediaDao  Context /com.tvplayer.webdav.data.database.MediaDatabase  MediaDao /com.tvplayer.webdav.data.database.MediaDatabase  
MediaDatabase /com.tvplayer.webdav.data.database.MediaDatabase  Volatile /com.tvplayer.webdav.data.database.MediaDatabase  Context 9com.tvplayer.webdav.data.database.MediaDatabase.Companion  MediaDao 9com.tvplayer.webdav.data.database.MediaDatabase.Companion  
MediaDatabase 9com.tvplayer.webdav.data.database.MediaDatabase.Companion  Volatile 9com.tvplayer.webdav.data.database.MediaDatabase.Companion  Double com.tvplayer.webdav.data.model  Double (com.tvplayer.webdav.data.model.MediaItem  
PrimaryKey (com.tvplayer.webdav.data.model.MediaItem  Boolean com.tvplayer.webdav.data.parser  Int com.tvplayer.webdav.data.parser  List com.tvplayer.webdav.data.parser  Map com.tvplayer.webdav.data.parser  Pattern com.tvplayer.webdav.data.parser  String com.tvplayer.webdav.data.parser  listOf com.tvplayer.webdav.data.parser  setOf com.tvplayer.webdav.data.parser  Boolean /com.tvplayer.webdav.data.parser.MediaFileParser  Int /com.tvplayer.webdav.data.parser.MediaFileParser  List /com.tvplayer.webdav.data.parser.MediaFileParser  Map /com.tvplayer.webdav.data.parser.MediaFileParser  Pattern /com.tvplayer.webdav.data.parser.MediaFileParser  String /com.tvplayer.webdav.data.parser.MediaFileParser  	getLISTOf /com.tvplayer.webdav.data.parser.MediaFileParser  	getListOf /com.tvplayer.webdav.data.parser.MediaFileParser  getSETOf /com.tvplayer.webdav.data.parser.MediaFileParser  getSetOf /com.tvplayer.webdav.data.parser.MediaFileParser  listOf /com.tvplayer.webdav.data.parser.MediaFileParser  setOf /com.tvplayer.webdav.data.parser.MediaFileParser  Boolean ;com.tvplayer.webdav.data.parser.MediaFileParser.ParseResult  Int ;com.tvplayer.webdav.data.parser.MediaFileParser.ParseResult  String ;com.tvplayer.webdav.data.parser.MediaFileParser.ParseResult  Int  com.tvplayer.webdav.data.scraper  List  com.tvplayer.webdav.data.scraper  Result  com.tvplayer.webdav.data.scraper  String  com.tvplayer.webdav.data.scraper  Int -com.tvplayer.webdav.data.scraper.MediaScraper  List -com.tvplayer.webdav.data.scraper.MediaScraper  
MediaDatabase -com.tvplayer.webdav.data.scraper.MediaScraper  MediaFileParser -com.tvplayer.webdav.data.scraper.MediaScraper  	MediaItem -com.tvplayer.webdav.data.scraper.MediaScraper  Result -com.tvplayer.webdav.data.scraper.MediaScraper  String -com.tvplayer.webdav.data.scraper.MediaScraper  
TmdbClient -com.tvplayer.webdav.data.scraper.MediaScraper  
WebDAVScanner -com.tvplayer.webdav.data.scraper.MediaScraper  Int 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  List 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  
MediaDatabase 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  MediaFileParser 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  	MediaItem 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  Result 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  String 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  
TmdbClient 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  
WebDAVScanner 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  Int Dcom.tvplayer.webdav.data.scraper.MediaScraper.ScrapeProgressCallback  String Dcom.tvplayer.webdav.data.scraper.MediaScraper.ScrapeProgressCallback  Int :com.tvplayer.webdav.data.scraper.MediaScraper.ScrapeResult  List :com.tvplayer.webdav.data.scraper.MediaScraper.ScrapeResult  	MediaItem :com.tvplayer.webdav.data.scraper.MediaScraper.ScrapeResult  Int com.tvplayer.webdav.data.webdav  Long com.tvplayer.webdav.data.webdav  TimeUnit com.tvplayer.webdav.data.webdav  Boolean -com.tvplayer.webdav.data.webdav.WebDAVScanner  Int -com.tvplayer.webdav.data.webdav.WebDAVScanner  List -com.tvplayer.webdav.data.webdav.WebDAVScanner  Long -com.tvplayer.webdav.data.webdav.WebDAVScanner  MediaFileParser -com.tvplayer.webdav.data.webdav.WebDAVScanner  OkHttpClient -com.tvplayer.webdav.data.webdav.WebDAVScanner  Result -com.tvplayer.webdav.data.webdav.WebDAVScanner  String -com.tvplayer.webdav.data.webdav.WebDAVScanner  TimeUnit -com.tvplayer.webdav.data.webdav.WebDAVScanner  Boolean 7com.tvplayer.webdav.data.webdav.WebDAVScanner.Companion  Int 7com.tvplayer.webdav.data.webdav.WebDAVScanner.Companion  List 7com.tvplayer.webdav.data.webdav.WebDAVScanner.Companion  Long 7com.tvplayer.webdav.data.webdav.WebDAVScanner.Companion  MediaFileParser 7com.tvplayer.webdav.data.webdav.WebDAVScanner.Companion  OkHttpClient 7com.tvplayer.webdav.data.webdav.WebDAVScanner.Companion  Result 7com.tvplayer.webdav.data.webdav.WebDAVScanner.Companion  String 7com.tvplayer.webdav.data.webdav.WebDAVScanner.Companion  TimeUnit 7com.tvplayer.webdav.data.webdav.WebDAVScanner.Companion  Int 8com.tvplayer.webdav.data.webdav.WebDAVScanner.ScanResult  List 8com.tvplayer.webdav.data.webdav.WebDAVScanner.ScanResult  Long 8com.tvplayer.webdav.data.webdav.WebDAVScanner.ScanResult  MediaFileParser 8com.tvplayer.webdav.data.webdav.WebDAVScanner.ScanResult  
WebDAVFile 8com.tvplayer.webdav.data.webdav.WebDAVScanner.ScanResult  Boolean 8com.tvplayer.webdav.data.webdav.WebDAVScanner.WebDAVFile  Long 8com.tvplayer.webdav.data.webdav.WebDAVScanner.WebDAVFile  String 8com.tvplayer.webdav.data.webdav.WebDAVScanner.WebDAVFile  ActivityScraperBinding .com.tvplayer.webdav.ui.scraper.ScraperActivity  Bundle .com.tvplayer.webdav.ui.scraper.ScraperActivity  MediaScraper .com.tvplayer.webdav.ui.scraper.ScraperActivity  
WebDAVScanner .com.tvplayer.webdav.ui.scraper.ScraperActivity  ActivityScraperBinding 8com.tvplayer.webdav.ui.scraper.ScraperActivity.Companion  Bundle 8com.tvplayer.webdav.ui.scraper.ScraperActivity.Companion  MediaScraper 8com.tvplayer.webdav.ui.scraper.ScraperActivity.Companion  
WebDAVScanner 8com.tvplayer.webdav.ui.scraper.ScraperActivity.Companion  StringReader java.io  
Converters 	java.lang  	MediaItem 	java.lang  OkHttpClient 	java.lang  OnConflictStrategy 	java.lang  TimeUnit 	java.lang  SECONDS java.util.concurrent.TimeUnit  Array kotlin  
Converters kotlin  Double kotlin  	MediaItem kotlin  OkHttpClient kotlin  OnConflictStrategy kotlin  TimeUnit kotlin  Volatile kotlin  arrayOf kotlin  
Converters kotlin.annotation  	MediaItem kotlin.annotation  OkHttpClient kotlin.annotation  OnConflictStrategy kotlin.annotation  TimeUnit kotlin.annotation  Volatile kotlin.annotation  
Converters kotlin.collections  Map kotlin.collections  	MediaItem kotlin.collections  OkHttpClient kotlin.collections  OnConflictStrategy kotlin.collections  TimeUnit kotlin.collections  Volatile kotlin.collections  
Converters kotlin.comparisons  	MediaItem kotlin.comparisons  OkHttpClient kotlin.comparisons  OnConflictStrategy kotlin.comparisons  TimeUnit kotlin.comparisons  Volatile kotlin.comparisons  
Converters 	kotlin.io  	MediaItem 	kotlin.io  OkHttpClient 	kotlin.io  OnConflictStrategy 	kotlin.io  TimeUnit 	kotlin.io  Volatile 	kotlin.io  
Converters 
kotlin.jvm  	MediaItem 
kotlin.jvm  OkHttpClient 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  TimeUnit 
kotlin.jvm  Volatile 
kotlin.jvm  
Converters 
kotlin.ranges  	MediaItem 
kotlin.ranges  OkHttpClient 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  TimeUnit 
kotlin.ranges  Volatile 
kotlin.ranges  
Converters kotlin.sequences  	MediaItem kotlin.sequences  OkHttpClient kotlin.sequences  OnConflictStrategy kotlin.sequences  TimeUnit kotlin.sequences  Volatile kotlin.sequences  
Converters kotlin.text  	MediaItem kotlin.text  OkHttpClient kotlin.text  OnConflictStrategy kotlin.text  TimeUnit kotlin.text  Volatile kotlin.text  delay kotlinx.coroutines  Flow kotlinx.coroutines.flow  Credentials okhttp3  Request okhttp3  Builder okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.OkHttpClient.Companion  
XmlPullParser org.xmlpull.v1  getRecentlyAdded *com.tvplayer.webdav.data.database.MediaDao  getContinueWatching *com.tvplayer.webdav.data.database.MediaDao  
asLiveData androidx.lifecycle  MediaDao androidx.lifecycle.ViewModel  
asLiveData androidx.lifecycle.ViewModel  getMediaByType *com.tvplayer.webdav.data.database.MediaDao  
asLiveData com.tvplayer.webdav.ui.home  MediaDao )com.tvplayer.webdav.ui.home.HomeViewModel  
asLiveData )com.tvplayer.webdav.ui.home.HomeViewModel  
getASLiveData )com.tvplayer.webdav.ui.home.HomeViewModel  
getAsLiveData )com.tvplayer.webdav.ui.home.HomeViewModel  mediaDao )com.tvplayer.webdav.ui.home.HomeViewModel  
asLiveData 	java.lang  
asLiveData kotlin  
asLiveData kotlin.annotation  
asLiveData kotlin.collections  
asLiveData kotlin.comparisons  
asLiveData 	kotlin.io  
asLiveData 
kotlin.jvm  
asLiveData 
kotlin.ranges  
asLiveData kotlin.sequences  
asLiveData kotlin.text  
asLiveData kotlinx.coroutines.flow.Flow  
getASLiveData kotlinx.coroutines.flow.Flow  
getAsLiveData kotlinx.coroutines.flow.Flow  TVInfo -com.tvplayer.webdav.data.scanner.MediaScanner  DatabaseModule com.tvplayer.webdav.di  ScrapingService  com.tvplayer.webdav.data.scraper  
ScrapingState 0com.tvplayer.webdav.data.scraper.ScrapingService  ScrapingService androidx.lifecycle.ViewModel  Int 4com.tvplayer.webdav.data.scanner.MediaScanner.TVInfo  String 4com.tvplayer.webdav.data.scanner.MediaScanner.TVInfo  Boolean  com.tvplayer.webdav.data.scraper  MediaDao  com.tvplayer.webdav.data.scraper  Unit  com.tvplayer.webdav.data.scraper  Boolean 0com.tvplayer.webdav.data.scraper.ScrapingService  Flow 0com.tvplayer.webdav.data.scraper.ScrapingService  Inject 0com.tvplayer.webdav.data.scraper.ScrapingService  Int 0com.tvplayer.webdav.data.scraper.ScrapingService  List 0com.tvplayer.webdav.data.scraper.ScrapingService  MediaDao 0com.tvplayer.webdav.data.scraper.ScrapingService  	MediaItem 0com.tvplayer.webdav.data.scraper.ScrapingService  MediaScanner 0com.tvplayer.webdav.data.scraper.ScrapingService  SimpleWebDAVClient 0com.tvplayer.webdav.data.scraper.ScrapingService  String 0com.tvplayer.webdav.data.scraper.ScrapingService  Unit 0com.tvplayer.webdav.data.scraper.ScrapingService  WebDAVServer 0com.tvplayer.webdav.data.scraper.ScrapingService  Boolean :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  Flow :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  Inject :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  Int :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  List :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  MediaDao :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  	MediaItem :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  MediaScanner :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  SimpleWebDAVClient :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  String :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  Unit :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  WebDAVServer :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  Int >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState  
ScrapingState >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState  String >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState  Int Hcom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.Completed  String Dcom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.Error  Int Ecom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.Saving  Int Gcom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.Scanning  String Gcom.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState.Scanning  MediaDao com.tvplayer.webdav.di  
MediaDatabase com.tvplayer.webdav.di  ApplicationContext %com.tvplayer.webdav.di.DatabaseModule  Context %com.tvplayer.webdav.di.DatabaseModule  MediaDao %com.tvplayer.webdav.di.DatabaseModule  
MediaDatabase %com.tvplayer.webdav.di.DatabaseModule  Provides %com.tvplayer.webdav.di.DatabaseModule  	Singleton %com.tvplayer.webdav.di.DatabaseModule  ScrapingService /com.tvplayer.webdav.ui.scanner.ScannerViewModel  WebDAVServer /com.tvplayer.webdav.ui.scanner.ScannerViewModel  _isScrapingActive /com.tvplayer.webdav.ui.scanner.ScannerViewModel  _scrapingState /com.tvplayer.webdav.ui.scanner.ScannerViewModel  ApplicationContext dagger.hilt.android.qualifiers  MediaDao 	java.lang  
MediaDatabase 	java.lang  MediaDao kotlin  
MediaDatabase kotlin  MediaDao kotlin.annotation  
MediaDatabase kotlin.annotation  MediaDao kotlin.collections  
MediaDatabase kotlin.collections  MediaDao kotlin.comparisons  
MediaDatabase kotlin.comparisons  MediaDao 	kotlin.io  
MediaDatabase 	kotlin.io  MediaDao 
kotlin.jvm  
MediaDatabase 
kotlin.jvm  MediaDao 
kotlin.ranges  
MediaDatabase 
kotlin.ranges  MediaDao kotlin.sequences  
MediaDatabase kotlin.sequences  MediaDao kotlin.text  
MediaDatabase kotlin.text  flow kotlinx.coroutines.flow  flowOn kotlinx.coroutines.flow  Float !com.tvplayer.webdav.data.database  Float *com.tvplayer.webdav.data.database.MediaDao  Gson com.google.gson  	TypeToken com.google.gson.reflect  Gson !com.tvplayer.webdav.data.database  Gson ,com.tvplayer.webdav.data.database.Converters  List ,com.tvplayer.webdav.data.database.Converters  String ,com.tvplayer.webdav.data.database.Converters  Gson 	java.lang  Gson kotlin  Gson kotlin.annotation  Gson kotlin.collections  Gson kotlin.comparisons  Gson 	kotlin.io  Gson 
kotlin.jvm  Gson 
kotlin.ranges  Gson kotlin.sequences  Gson kotlin.text  Gson com.tvplayer.webdav.data.tmdb  Gson (com.tvplayer.webdav.data.tmdb.TmdbClient  Gson 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  Gson 	java.util  android androidx.fragment.app.Fragment  layout_bottom_info com.tvplayer.webdav.R.id  android com.tvplayer.webdav.ui.home  android (com.tvplayer.webdav.ui.home.HomeFragment  android 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  Unit /com.tvplayer.webdav.ui.home.PosterFocusAnimator  android 	java.lang  android kotlin  android kotlin.annotation  android kotlin.collections  android kotlin.comparisons  android 	kotlin.io  android 
kotlin.jvm  android 
kotlin.ranges  android kotlin.sequences  android kotlin.text  WebDAVServerDao !com.tvplayer.webdav.data.database  TVInfo /com.tvplayer.webdav.data.parser.MediaFileParser  	StateFlow androidx.lifecycle.ViewModel  WebDAVServerDao androidx.lifecycle.ViewModel  
Converters 
androidx.room  	MediaItem 
androidx.room  Transaction 
androidx.room  Volatile 
androidx.room  WebDAVServer 
androidx.room  WebDAVServerDao androidx.room.RoomDatabase  Database !com.tvplayer.webdav.data.database  RoomDatabase !com.tvplayer.webdav.data.database  Transaction !com.tvplayer.webdav.data.database  TypeConverters !com.tvplayer.webdav.data.database  WebDAVServer !com.tvplayer.webdav.data.database  	MediaType ,com.tvplayer.webdav.data.database.Converters  LiveData *com.tvplayer.webdav.data.database.MediaDao  	MediaType *com.tvplayer.webdav.data.database.MediaDao  WebDAVServerDao /com.tvplayer.webdav.data.database.MediaDatabase  WebDAVServerDao 9com.tvplayer.webdav.data.database.MediaDatabase.Companion  Boolean 1com.tvplayer.webdav.data.database.WebDAVServerDao  Delete 1com.tvplayer.webdav.data.database.WebDAVServerDao  Flow 1com.tvplayer.webdav.data.database.WebDAVServerDao  Insert 1com.tvplayer.webdav.data.database.WebDAVServerDao  Int 1com.tvplayer.webdav.data.database.WebDAVServerDao  List 1com.tvplayer.webdav.data.database.WebDAVServerDao  LiveData 1com.tvplayer.webdav.data.database.WebDAVServerDao  Long 1com.tvplayer.webdav.data.database.WebDAVServerDao  OnConflictStrategy 1com.tvplayer.webdav.data.database.WebDAVServerDao  Query 1com.tvplayer.webdav.data.database.WebDAVServerDao  String 1com.tvplayer.webdav.data.database.WebDAVServerDao  Transaction 1com.tvplayer.webdav.data.database.WebDAVServerDao  Update 1com.tvplayer.webdav.data.database.WebDAVServerDao  WebDAVServer 1com.tvplayer.webdav.data.database.WebDAVServerDao  getAllServersLiveData 1com.tvplayer.webdav.data.database.WebDAVServerDao  getDefaultServerLiveData 1com.tvplayer.webdav.data.database.WebDAVServerDao  
PrimaryKey +com.tvplayer.webdav.data.model.WebDAVServer  Inject /com.tvplayer.webdav.data.parser.MediaFileParser  	MediaType /com.tvplayer.webdav.data.parser.MediaFileParser  Inject 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  Int 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  	MediaType 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  Pattern 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  String 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  	getLISTOf 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  	getListOf 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  getSETOf 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  getSetOf 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  listOf 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  setOf 9com.tvplayer.webdav.data.parser.MediaFileParser.Companion  	MediaType ;com.tvplayer.webdav.data.parser.MediaFileParser.ParseResult  Int 6com.tvplayer.webdav.data.parser.MediaFileParser.TVInfo  String 6com.tvplayer.webdav.data.parser.MediaFileParser.TVInfo  MutableStateFlow  com.tvplayer.webdav.data.scraper  
ScrapingState  com.tvplayer.webdav.data.scraper  asStateFlow  com.tvplayer.webdav.data.scraper  setOf  com.tvplayer.webdav.data.scraper  Boolean -com.tvplayer.webdav.data.scraper.MediaScraper  Inject -com.tvplayer.webdav.data.scraper.MediaScraper  MediaDao -com.tvplayer.webdav.data.scraper.MediaScraper  SimpleWebDAVClient -com.tvplayer.webdav.data.scraper.MediaScraper  
WebDAVFile -com.tvplayer.webdav.data.scraper.MediaScraper  setOf -com.tvplayer.webdav.data.scraper.MediaScraper  Boolean 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  Inject 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  MediaDao 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  SimpleWebDAVClient 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  
WebDAVFile 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  getSETOf 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  getSetOf 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  setOf 7com.tvplayer.webdav.data.scraper.MediaScraper.Companion  ScrapeResult Dcom.tvplayer.webdav.data.scraper.MediaScraper.ScrapeProgressCallback  String :com.tvplayer.webdav.data.scraper.MediaScraper.ScrapeResult  MediaScraper 0com.tvplayer.webdav.data.scraper.ScrapingService  MutableStateFlow 0com.tvplayer.webdav.data.scraper.ScrapingService  	StateFlow 0com.tvplayer.webdav.data.scraper.ScrapingService  _isScrapingActive 0com.tvplayer.webdav.data.scraper.ScrapingService  _scrapingState 0com.tvplayer.webdav.data.scraper.ScrapingService  asStateFlow 0com.tvplayer.webdav.data.scraper.ScrapingService  getASStateFlow 0com.tvplayer.webdav.data.scraper.ScrapingService  getAsStateFlow 0com.tvplayer.webdav.data.scraper.ScrapingService  getContinueWatching 0com.tvplayer.webdav.data.scraper.ScrapingService  	getMovies 0com.tvplayer.webdav.data.scraper.ScrapingService  getRecentlyAdded 0com.tvplayer.webdav.data.scraper.ScrapingService  
getTVShows 0com.tvplayer.webdav.data.scraper.ScrapingService  isScrapingActive 0com.tvplayer.webdav.data.scraper.ScrapingService  
scrapingState 0com.tvplayer.webdav.data.scraper.ScrapingService  MediaScraper :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  MutableStateFlow :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  
ScrapingState :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  	StateFlow :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  asStateFlow :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  getASStateFlow :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  getAsStateFlow :com.tvplayer.webdav.data.scraper.ScrapingService.Companion  Boolean >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState  List >com.tvplayer.webdav.data.scraper.ScrapingService.ScrapingState  WebDAVServerDao %com.tvplayer.webdav.di.DatabaseModule  ScrapingService )com.tvplayer.webdav.ui.home.HomeViewModel  scrapingService )com.tvplayer.webdav.ui.home.HomeViewModel  	StateFlow /com.tvplayer.webdav.ui.scanner.ScannerViewModel  _error /com.tvplayer.webdav.ui.scanner.ScannerViewModel  
_isLoading /com.tvplayer.webdav.ui.scanner.ScannerViewModel  scrapingService /com.tvplayer.webdav.ui.scanner.ScannerViewModel  List com.tvplayer.webdav.ui.webdav  List 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  WebDAVServerDao 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  webdavServerDao 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  MutableStateFlow 	java.lang  
ScrapingState 	java.lang  WebDAVServer 	java.lang  asStateFlow 	java.lang  MutableStateFlow kotlin  
ScrapingState kotlin  WebDAVServer kotlin  asStateFlow kotlin  MutableStateFlow kotlin.annotation  
ScrapingState kotlin.annotation  WebDAVServer kotlin.annotation  asStateFlow kotlin.annotation  MutableStateFlow kotlin.collections  
ScrapingState kotlin.collections  WebDAVServer kotlin.collections  asStateFlow kotlin.collections  MutableStateFlow kotlin.comparisons  
ScrapingState kotlin.comparisons  WebDAVServer kotlin.comparisons  asStateFlow kotlin.comparisons  MutableStateFlow 	kotlin.io  
ScrapingState 	kotlin.io  WebDAVServer 	kotlin.io  asStateFlow 	kotlin.io  MutableStateFlow 
kotlin.jvm  
ScrapingState 
kotlin.jvm  WebDAVServer 
kotlin.jvm  asStateFlow 
kotlin.jvm  MutableStateFlow 
kotlin.ranges  
ScrapingState 
kotlin.ranges  WebDAVServer 
kotlin.ranges  asStateFlow 
kotlin.ranges  MutableStateFlow kotlin.sequences  
ScrapingState kotlin.sequences  WebDAVServer kotlin.sequences  asStateFlow kotlin.sequences  MutableStateFlow kotlin.text  
ScrapingState kotlin.text  WebDAVServer kotlin.text  asStateFlow kotlin.text  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  	MediaItem androidx.fragment.app.Fragment  	MediaItem (com.tvplayer.webdav.ui.home.HomeFragment  	MediaItem 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  MediaDao com.tvplayer.webdav.ui.home  ScrapingService com.tvplayer.webdav.ui.home  WebDAVServerDao com.tvplayer.webdav.ui.webdav  ScrapingService 	java.lang  WebDAVServerDao 	java.lang  ScrapingService kotlin  WebDAVServerDao kotlin  ScrapingService kotlin.annotation  WebDAVServerDao kotlin.annotation  ScrapingService kotlin.collections  WebDAVServerDao kotlin.collections  ScrapingService kotlin.comparisons  WebDAVServerDao kotlin.comparisons  ScrapingService 	kotlin.io  WebDAVServerDao 	kotlin.io  ScrapingService 
kotlin.jvm  WebDAVServerDao 
kotlin.jvm  ScrapingService 
kotlin.ranges  WebDAVServerDao 
kotlin.ranges  ScrapingService kotlin.sequences  WebDAVServerDao kotlin.sequences  ScrapingService kotlin.text  WebDAVServerDao kotlin.text  first kotlinx.coroutines.flow  WebDAVServerStorage  com.tvplayer.webdav.data.storage  
StorageModule com.tvplayer.webdav.di  SharedPreferences android.content  WebDAVServerStorage androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  Inject 4com.tvplayer.webdav.data.storage.WebDAVServerStorage  SharedPreferences 4com.tvplayer.webdav.data.storage.WebDAVServerStorage  WebDAVServer 4com.tvplayer.webdav.data.storage.WebDAVServerStorage  Inject >com.tvplayer.webdav.data.storage.WebDAVServerStorage.Companion  SharedPreferences >com.tvplayer.webdav.data.storage.WebDAVServerStorage.Companion  WebDAVServer >com.tvplayer.webdav.data.storage.WebDAVServerStorage.Companion  ApplicationContext $com.tvplayer.webdav.di.StorageModule  Context $com.tvplayer.webdav.di.StorageModule  Provides $com.tvplayer.webdav.di.StorageModule  SharedPreferences $com.tvplayer.webdav.di.StorageModule  	Singleton $com.tvplayer.webdav.di.StorageModule  WebDAVServerStorage $com.tvplayer.webdav.di.StorageModule  MediaScanner )com.tvplayer.webdav.ui.home.HomeViewModel  WebDAVServerStorage )com.tvplayer.webdav.ui.home.HomeViewModel  com com.tvplayer.webdav.ui.scanner  com /com.tvplayer.webdav.ui.scanner.ScannerViewModel  WebDAVServerStorage 7com.tvplayer.webdav.ui.webdav.WebDAVConnectionViewModel  
WebDAVFile androidx.lifecycle.ViewModel  SimpleWebDAVClient /com.tvplayer.webdav.ui.scanner.ScannerViewModel  
WebDAVFile /com.tvplayer.webdav.ui.scanner.ScannerViewModel  WebDAVServerStorage /com.tvplayer.webdav.ui.scanner.ScannerViewModel  _browseError /com.tvplayer.webdav.ui.scanner.ScannerViewModel  _currentPath /com.tvplayer.webdav.ui.scanner.ScannerViewModel  _directoryItems /com.tvplayer.webdav.ui.scanner.ScannerViewModel  ModeHint -com.tvplayer.webdav.data.scanner.MediaScanner  String androidx.fragment.app.Fragment  Unit androidx.fragment.app.Fragment  String  com.tvplayer.webdav.data.storage  String 4com.tvplayer.webdav.data.storage.WebDAVServerStorage  String >com.tvplayer.webdav.data.storage.WebDAVServerStorage.Companion  Unit com.tvplayer.webdav.ui.scanner  String .com.tvplayer.webdav.ui.scanner.ScannerFragment  Unit .com.tvplayer.webdav.ui.scanner.ScannerFragment  String 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  Unit 8com.tvplayer.webdav.ui.scanner.ScannerFragment.Companion  setMoviesDir 4com.tvplayer.webdav.data.storage.WebDAVServerStorage  setTvDir 4com.tvplayer.webdav.data.storage.WebDAVServerStorage  
serverStorage /com.tvplayer.webdav.ui.scanner.ScannerViewModel  
MediaCache  com.tvplayer.webdav.data.storage  MediaModule com.tvplayer.webdav.di  Transformations androidx.lifecycle  List  com.tvplayer.webdav.data.storage  MutableLiveData  com.tvplayer.webdav.data.storage  	emptyList  com.tvplayer.webdav.data.storage  Inject +com.tvplayer.webdav.data.storage.MediaCache  List +com.tvplayer.webdav.data.storage.MediaCache  LiveData +com.tvplayer.webdav.data.storage.MediaCache  	MediaItem +com.tvplayer.webdav.data.storage.MediaCache  MutableLiveData +com.tvplayer.webdav.data.storage.MediaCache  	emptyList +com.tvplayer.webdav.data.storage.MediaCache  getEMPTYList +com.tvplayer.webdav.data.storage.MediaCache  getEmptyList +com.tvplayer.webdav.data.storage.MediaCache  
MediaCache "com.tvplayer.webdav.di.MediaModule  Provides "com.tvplayer.webdav.di.MediaModule  	Singleton "com.tvplayer.webdav.di.MediaModule  com )com.tvplayer.webdav.ui.home.HomeViewModel  	emptyList 	java.lang  	emptyList kotlin  	emptyList kotlin.annotation  	emptyList kotlin.collections  	emptyList kotlin.comparisons  	emptyList 	kotlin.io  	emptyList 
kotlin.jvm  	emptyList 
kotlin.ranges  	emptyList kotlin.sequences  	emptyList kotlin.text  MediatorLiveData androidx.lifecycle  List (com.tvplayer.webdav.data.tmdb.TmdbClient  List 2com.tvplayer.webdav.data.tmdb.TmdbClient.Companion  TmdbTranslationsResponse com.tvplayer.webdav.data.tmdb  TmdbTranslation com.tvplayer.webdav.data.tmdb  TmdbTranslationData com.tvplayer.webdav.data.tmdb  TmdbTranslationsResponse ,com.tvplayer.webdav.data.tmdb.TmdbApiService  TmdbTranslationsResponse 6com.tvplayer.webdav.data.tmdb.TmdbApiService.Companion  String -com.tvplayer.webdav.data.tmdb.TmdbTranslation  TmdbTranslationData -com.tvplayer.webdav.data.tmdb.TmdbTranslation  String 1com.tvplayer.webdav.data.tmdb.TmdbTranslationData  Int 6com.tvplayer.webdav.data.tmdb.TmdbTranslationsResponse  List 6com.tvplayer.webdav.data.tmdb.TmdbTranslationsResponse  TmdbTranslation 6com.tvplayer.webdav.data.tmdb.TmdbTranslationsResponse  Gson  com.tvplayer.webdav.data.storage  Gson +com.tvplayer.webdav.data.storage.MediaCache  SharedPreferences +com.tvplayer.webdav.data.storage.MediaCache  loadPersistedItems +com.tvplayer.webdav.data.storage.MediaCache  Gson 5com.tvplayer.webdav.data.storage.MediaCache.Companion  Inject 5com.tvplayer.webdav.data.storage.MediaCache.Companion  List 5com.tvplayer.webdav.data.storage.MediaCache.Companion  LiveData 5com.tvplayer.webdav.data.storage.MediaCache.Companion  	MediaItem 5com.tvplayer.webdav.data.storage.MediaCache.Companion  MutableLiveData 5com.tvplayer.webdav.data.storage.MediaCache.Companion  SharedPreferences 5com.tvplayer.webdav.data.storage.MediaCache.Companion  SharedPreferences "com.tvplayer.webdav.di.MediaModule  TVSeriesAdapter com.tvplayer.webdav.ui.home  TVSeriesDiffCallback +com.tvplayer.webdav.ui.home.TVSeriesAdapter  TVSeriesViewHolder +com.tvplayer.webdav.ui.home.TVSeriesAdapter  TVSeriesSummary com.tvplayer.webdav.data.model  TVSeriesAdapter androidx.fragment.app.Fragment  TVSeriesSummary androidx.fragment.app.Fragment  TVSeriesSummary androidx.lifecycle.ViewModel  TVSeriesSummary 2androidx.recyclerview.widget.DiffUtil.ItemCallback  TVSeriesSummary (androidx.recyclerview.widget.ListAdapter  TVSeriesSummary 1androidx.recyclerview.widget.RecyclerView.Adapter  TVSeriesSummary 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Boolean .com.tvplayer.webdav.data.model.TVSeriesSummary  Date .com.tvplayer.webdav.data.model.TVSeriesSummary  Float .com.tvplayer.webdav.data.model.TVSeriesSummary  Int .com.tvplayer.webdav.data.model.TVSeriesSummary  List .com.tvplayer.webdav.data.model.TVSeriesSummary  	MediaItem .com.tvplayer.webdav.data.model.TVSeriesSummary  String .com.tvplayer.webdav.data.model.TVSeriesSummary  TVSeriesSummary +com.tvplayer.webdav.data.storage.MediaCache  TVSeriesSummary 5com.tvplayer.webdav.data.storage.MediaCache.Companion  TVSeriesAdapter (com.tvplayer.webdav.ui.home.HomeFragment  TVSeriesSummary (com.tvplayer.webdav.ui.home.HomeFragment  TVSeriesAdapter 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  TVSeriesSummary 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  TVSeriesSummary )com.tvplayer.webdav.ui.home.HomeViewModel  Boolean +com.tvplayer.webdav.ui.home.TVSeriesAdapter  DiffUtil +com.tvplayer.webdav.ui.home.TVSeriesAdapter  	ImageView +com.tvplayer.webdav.ui.home.TVSeriesAdapter  Int +com.tvplayer.webdav.ui.home.TVSeriesAdapter  ProgressBar +com.tvplayer.webdav.ui.home.TVSeriesAdapter  R +com.tvplayer.webdav.ui.home.TVSeriesAdapter  RecyclerView +com.tvplayer.webdav.ui.home.TVSeriesAdapter  TVSeriesSummary +com.tvplayer.webdav.ui.home.TVSeriesAdapter  TextView +com.tvplayer.webdav.ui.home.TVSeriesAdapter  Unit +com.tvplayer.webdav.ui.home.TVSeriesAdapter  View +com.tvplayer.webdav.ui.home.TVSeriesAdapter  	ViewGroup +com.tvplayer.webdav.ui.home.TVSeriesAdapter  Boolean @com.tvplayer.webdav.ui.home.TVSeriesAdapter.TVSeriesDiffCallback  TVSeriesSummary @com.tvplayer.webdav.ui.home.TVSeriesAdapter.TVSeriesDiffCallback  	ImageView >com.tvplayer.webdav.ui.home.TVSeriesAdapter.TVSeriesViewHolder  ProgressBar >com.tvplayer.webdav.ui.home.TVSeriesAdapter.TVSeriesViewHolder  R >com.tvplayer.webdav.ui.home.TVSeriesAdapter.TVSeriesViewHolder  TVSeriesSummary >com.tvplayer.webdav.ui.home.TVSeriesAdapter.TVSeriesViewHolder  TextView >com.tvplayer.webdav.ui.home.TVSeriesAdapter.TVSeriesViewHolder  Unit >com.tvplayer.webdav.ui.home.TVSeriesAdapter.TVSeriesViewHolder  View >com.tvplayer.webdav.ui.home.TVSeriesAdapter.TVSeriesViewHolder  String (com.tvplayer.webdav.ui.home.HomeFragment  String 2com.tvplayer.webdav.ui.home.HomeFragment.Companion  VideoDetailsFragment com.tvplayer.webdav.ui.details  VideoDetailsViewModel com.tvplayer.webdav.ui.details  VideoDetailsActivity com.tvplayer.webdav.ui.details  	ImageView androidx.fragment.app.Fragment  VideoDetailsFragment androidx.fragment.app.Fragment  VideoDetailsViewModel androidx.fragment.app.Fragment  Boolean com.tvplayer.webdav.ui.details  MutableLiveData com.tvplayer.webdav.ui.details  String com.tvplayer.webdav.ui.details  getValue com.tvplayer.webdav.ui.details  provideDelegate com.tvplayer.webdav.ui.details  
viewModels com.tvplayer.webdav.ui.details  Bundle 3com.tvplayer.webdav.ui.details.VideoDetailsActivity  Bundle 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  Button 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  	ImageView 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  LayoutInflater 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  	MediaItem 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  TextView 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  VideoDetailsFragment 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  VideoDetailsViewModel 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  View 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  	ViewGroup 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  getGETValue 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  getGetValue 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  getPROVIDEDelegate 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  getProvideDelegate 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  
getVIEWModels 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  getValue 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  
getViewModels 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  provideDelegate 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  
viewModels 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  Bundle =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  Button =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  	ImageView =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  LayoutInflater =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  	MediaItem =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  TextView =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  VideoDetailsFragment =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  VideoDetailsViewModel =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  View =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  	ViewGroup =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  getGETValue =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  getGetValue =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  getPROVIDEDelegate =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  getProvideDelegate =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  getValue =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  provideDelegate =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  
viewModels =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  Boolean 4com.tvplayer.webdav.ui.details.VideoDetailsViewModel  Inject 4com.tvplayer.webdav.ui.details.VideoDetailsViewModel  LiveData 4com.tvplayer.webdav.ui.details.VideoDetailsViewModel  	MediaItem 4com.tvplayer.webdav.ui.details.VideoDetailsViewModel  MutableLiveData 4com.tvplayer.webdav.ui.details.VideoDetailsViewModel  String 4com.tvplayer.webdav.ui.details.VideoDetailsViewModel  _error 4com.tvplayer.webdav.ui.details.VideoDetailsViewModel  
_isLoading 4com.tvplayer.webdav.ui.details.VideoDetailsViewModel  
_mediaItem 4com.tvplayer.webdav.ui.details.VideoDetailsViewModel  KeyEvent android.view  ActorViewHolder +com.tvplayer.webdav.ui.details.ActorAdapter  ActorAdapter com.tvplayer.webdav.ui.details  Actor com.tvplayer.webdav.data.model  Adapter )androidx.recyclerview.widget.RecyclerView  Actor 1androidx.recyclerview.widget.RecyclerView.Adapter  List 1androidx.recyclerview.widget.RecyclerView.Adapter  Actor 4androidx.recyclerview.widget.RecyclerView.ViewHolder  iv_actor_avatar com.tvplayer.webdav.R.id  
tv_actor_name com.tvplayer.webdav.R.id  
tv_actor_role com.tvplayer.webdav.R.id  Boolean $com.tvplayer.webdav.data.model.Actor  String $com.tvplayer.webdav.data.model.Actor  Int com.tvplayer.webdav.ui.details  List com.tvplayer.webdav.ui.details  R com.tvplayer.webdav.ui.details  Unit com.tvplayer.webdav.ui.details  Actor +com.tvplayer.webdav.ui.details.ActorAdapter  	ImageView +com.tvplayer.webdav.ui.details.ActorAdapter  Int +com.tvplayer.webdav.ui.details.ActorAdapter  List +com.tvplayer.webdav.ui.details.ActorAdapter  R +com.tvplayer.webdav.ui.details.ActorAdapter  RecyclerView +com.tvplayer.webdav.ui.details.ActorAdapter  TextView +com.tvplayer.webdav.ui.details.ActorAdapter  Unit +com.tvplayer.webdav.ui.details.ActorAdapter  View +com.tvplayer.webdav.ui.details.ActorAdapter  	ViewGroup +com.tvplayer.webdav.ui.details.ActorAdapter  Actor ;com.tvplayer.webdav.ui.details.ActorAdapter.ActorViewHolder  	ImageView ;com.tvplayer.webdav.ui.details.ActorAdapter.ActorViewHolder  R ;com.tvplayer.webdav.ui.details.ActorAdapter.ActorViewHolder  TextView ;com.tvplayer.webdav.ui.details.ActorAdapter.ActorViewHolder  View ;com.tvplayer.webdav.ui.details.ActorAdapter.ActorViewHolder  RecyclerView 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  RecyclerView =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  LinearLayout androidx.fragment.app.Fragment  
ScrollView androidx.fragment.app.Fragment  LinearLayout com.tvplayer.webdav.ui.details  
ScrollView com.tvplayer.webdav.ui.details  LinearLayout 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  
ScrollView 3com.tvplayer.webdav.ui.details.VideoDetailsFragment  LinearLayout =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  
ScrollView =com.tvplayer.webdav.ui.details.VideoDetailsFragment.Companion  LinearLayout 	java.lang  
ScrollView 	java.lang  LinearLayout kotlin  
ScrollView kotlin  LinearLayout kotlin.annotation  
ScrollView kotlin.annotation  LinearLayout kotlin.collections  
ScrollView kotlin.collections  LinearLayout kotlin.comparisons  
ScrollView kotlin.comparisons  LinearLayout 	kotlin.io  
ScrollView 	kotlin.io  LinearLayout 
kotlin.jvm  
ScrollView 
kotlin.jvm  LinearLayout 
kotlin.ranges  
ScrollView 
kotlin.ranges  LinearLayout kotlin.sequences  
ScrollView kotlin.sequences  LinearLayout kotlin.text  
ScrollView kotlin.text  GestureDetector android.view  MotionEvent android.view  LinearLayout android.widget  
ScrollView android.widget  abs kotlin.math                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 