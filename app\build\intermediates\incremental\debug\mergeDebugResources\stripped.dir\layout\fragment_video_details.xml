<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <!-- 全屏背景封面图 -->
    <ImageView
        android:id="@+id/iv_backdrop"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <!-- 底部渐变遮罩 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bottom_gradient" />

    <!-- 底部内容区域 -->
    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="bottom"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="48dp"
            android:paddingEnd="48dp"
            android:paddingTop="32dp"
            android:paddingBottom="48dp"
            android:minHeight="0dp">

        <!-- 电影标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="电影标题"
            android:textSize="42sp"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:layout_marginBottom="16dp"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- 信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical">

            <!-- 评分 -->
            <TextView
                android:id="@+id/tv_rating"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="6.1"
                android:textSize="14sp"
                android:textColor="@color/text_primary"
                android:background="@drawable/rating_badge_background"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:layout_marginEnd="12dp" />

            <!-- 年份 -->
            <TextView
                android:id="@+id/tv_year"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2025"
                android:textSize="16sp"
                android:textColor="@color/text_primary"
                android:layout_marginEnd="16dp" />

            <!-- 时长 -->
            <TextView
                android:id="@+id/tv_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1小时54分钟"
                android:textSize="16sp"
                android:textColor="@color/text_primary"
                android:layout_marginEnd="16dp" />

            <!-- 类型 -->
            <TextView
                android:id="@+id/tv_genre"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="剧情 喜剧"
                android:textSize="16sp"
                android:textColor="@color/text_primary"
                android:layout_marginEnd="16dp" />

            <!-- 文件大小 -->
            <TextView
                android:id="@+id/tv_file_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="24.07 GB"
                android:textSize="16sp"
                android:textColor="@color/text_primary" />

        </LinearLayout>

        <!-- 简介显示 -->
        <TextView
            android:id="@+id/tv_overview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="这里是电影简介内容..."
            android:textSize="16sp"
            android:textColor="@color/text_primary"
            android:lineSpacingExtra="4dp"
            android:layout_marginBottom="24dp" />

        <!-- 播放按钮 -->
        <Button
            android:id="@+id/btn_play"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="播放"
            android:textSize="18sp"
            android:textColor="@color/text_primary"
            android:background="@drawable/btn_play_background"
            android:paddingStart="32dp"
            android:paddingEnd="32dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:layout_marginBottom="48dp" />

        <!-- 演员表分隔空间 - 用于下滑显示 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="200dp" />

        <!-- 相关演员标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="相关演员"
            android:textSize="24sp"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- 演员列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_actors"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:clipToPadding="false"
            android:paddingStart="0dp"
            android:paddingEnd="24dp" />

        <!-- 视频详情信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp">

            <!-- 文件名和技术信息 -->
            <TextView
                android:id="@+id/tv_filename"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="人生开门红.2025.2160p.DV.WEB-DL.H265.60FPS.DTS.5.1.mp4"
                android:textSize="14sp"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <!-- 来源路径 -->
            <TextView
                android:id="@+id/tv_source_path"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="WebDAV: 我的WebDAV-/夸克网盘/电影电视/电影/"
                android:textSize="14sp"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <!-- 时长和大小 -->
            <TextView
                android:id="@+id/tv_duration_size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="1小时54分钟 其他 24.07 GB"
                android:textSize="14sp"
                android:textColor="@color/text_primary" />

        </LinearLayout>

        <!-- 返回顶部按钮 -->
        <LinearLayout
            android:id="@+id/btn_back_to_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_gravity="center_horizontal"
            android:focusable="true"
            android:clickable="true"
            android:background="@drawable/back_to_top_background"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:layout_marginBottom="32dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_expand_less"
                android:tint="@color/text_primary"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="返回到顶部"
                android:textSize="14sp"
                android:textColor="@color/text_primary" />

        </LinearLayout>

        </LinearLayout>
    </ScrollView>

</FrameLayout>
